"""
核心控制器模块
负责协调各个模块的工作，实现音乐视频生成的主要业务逻辑
统一管理音频获取、字幕生成、视频合成等流程
"""

import os
from pathlib import Path
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass
from PySide6.QtCore import QObject, Signal, QThread, QTimer
import time

from src.logger.logger import get_logger
from src.config.settings import config
from src.audio.audio_manager import audio_manager, AudioInfo
from src.subtitle.subtitle_manager import subtitle_manager
from src.video.video_manager import video_manager


@dataclass
class TaskConfig:
    """任务配置类"""
    song_title: str = ""
    artist: str = ""
    keywords: List[str] = None
    video_count: int = 5
    output_path: str = ""
    audio_source: str = "mock"
    video_source: str = "mock"
    lyric_provider: str = "mock"
    # AI相关配置
    use_ai_generation: bool = False
    analysis_model: str = ""
    image_model: str = ""
    image_style: str = ""
    custom_style: str = ""

    def __post_init__(self):
        if self.keywords is None:
            self.keywords = []


@dataclass
class TaskProgress:
    """任务进度类"""
    stage: str = "准备中"
    progress: float = 0.0
    message: str = ""
    error: str = ""
    completed: bool = False


class VideoGenerationWorker(QThread):
    """视频生成工作线程"""
    
    # 进度更新信号
    progress_updated = Signal(TaskProgress)
    # 任务完成信号
    task_completed = Signal(bool, str)
    
    def __init__(self, task_config: TaskConfig):
        super().__init__()
        self.task_config = task_config
        self.logger = get_logger("VideoGenerationWorker")
        self.should_stop = False
    
    def stop(self):
        """停止任务"""
        self.should_stop = True
    
    def run(self):
        """执行视频生成任务"""
        try:
            self._emit_progress("开始生成", 0.0, "初始化任务...")
            
            if self.should_stop:
                return
            
            # 1. 搜索和下载音频
            audio_info = self._download_audio()
            if not audio_info:
                self._emit_error("音频下载失败")
                return
            
            if self.should_stop:
                return
            
            # 2. 获取歌词并生成字幕
            subtitle_path = self._create_subtitle(audio_info)
            
            if self.should_stop:
                return
            
            # 3. 搜索和下载视频素材
            video_files = self._download_videos()
            if not video_files:
                self._emit_error("视频素材下载失败")
                return
            
            if self.should_stop:
                return
            
            # 4. 合成最终视频
            success = self._compose_video(audio_info, video_files, subtitle_path)
            
            if success:
                self._emit_progress("完成", 100.0, "视频生成成功！")
                self.task_completed.emit(True, self.task_config.output_path)
            else:
                self._emit_error("视频合成失败")
            
        except Exception as e:
            self.logger.exception("视频生成过程中发生异常")
            self._emit_error(f"生成失败: {str(e)}")
    
    def _download_audio(self) -> Optional[AudioInfo]:
        """下载音频"""
        self._emit_progress("音频处理", 10.0, "搜索音频...")

        # 首先尝试主要音频源
        search_results = audio_manager.search_audio(
            self.task_config.song_title,
            self.task_config.artist,
            self.task_config.audio_source
        )

        if not search_results:
            self._emit_progress("音频处理", 15.0, "主要音频源无结果，尝试备用源...")
            # 如果主要源失败，尝试YouTube
            if self.task_config.audio_source != "youtube":
                search_results = audio_manager.search_audio(
                    self.task_config.song_title,
                    self.task_config.artist,
                    "youtube"
                )

        if not search_results:
            self.logger.error("所有音频源都无法找到歌曲")
            return None

        self._emit_progress("音频处理", 20.0, "下载音频...")

        # 尝试下载前几个搜索结果
        for i, result in enumerate(search_results[:3]):  # 最多尝试3个结果
            try:
                audio_url = result.get('url', '')
                filename = f"{self.task_config.song_title}_{self.task_config.artist}_{i}.mp3"

                # 确定使用的音频源
                source = self.task_config.audio_source
                if 'youtube.com' in audio_url or 'youtu.be' in audio_url:
                    source = "youtube"

                audio_info = audio_manager.download_audio(audio_url, filename, source)

                if audio_info:
                    self._emit_progress("音频处理", 30.0, f"音频下载完成: {audio_info.title}")
                    return audio_info
                else:
                    self._emit_progress("音频处理", 20.0 + i * 3, f"尝试下载第{i+1}个结果失败，继续尝试...")

            except Exception as e:
                self.logger.warning(f"下载第{i+1}个音频结果失败: {e}")
                continue

        # 提供详细的错误信息和建议
        error_msg = "音频下载失败。可能的原因和解决方案：\n"
        error_msg += "1. 网易云音乐：版权保护，无法下载\n"
        error_msg += "2. YouTube：网络连接问题或地区限制\n"
        error_msg += "3. 建议：选择'local'音频源，使用本地音频文件\n"
        error_msg += "4. 或者将音频文件放在Music/Downloads文件夹中"

        self.logger.error("所有音频下载尝试都失败了")
        self.logger.info(error_msg)
        return None
    
    def _create_subtitle(self, audio_info: AudioInfo) -> Optional[str]:
        """创建字幕"""
        self._emit_progress("字幕处理", 40.0, "获取歌词...")
        
        # 获取歌词
        lyrics_content = subtitle_manager.get_lyrics(
            self.task_config.song_title,
            self.task_config.artist,
            self.task_config.lyric_provider
        )
        
        if not lyrics_content:
            self.logger.warning("未能获取歌词，将跳过字幕生成")
            return None
        
        self._emit_progress("字幕处理", 50.0, "生成字幕文件...")
        
        # 生成字幕文件
        subtitle_filename = f"{self.task_config.song_title}_{self.task_config.artist}.srt"
        subtitle_path = config.get_temp_dir() / "subtitle" / subtitle_filename
        
        success = subtitle_manager.create_subtitle(
            lyrics_content,
            audio_info.duration,
            str(subtitle_path),
            "srt"
        )
        
        if success:
            self._emit_progress("字幕处理", 60.0, "字幕文件生成完成")
            return str(subtitle_path)
        
        return None
    
    def _download_videos(self) -> List[str]:
        """下载视频素材或生成AI图片"""
        if self.task_config.use_ai_generation:
            return self._generate_ai_videos()
        else:
            return self._download_traditional_videos()

    def _generate_ai_videos(self) -> List[str]:
        """使用AI生成图片并转换为视频"""
        self._emit_progress("AI生成", 65.0, "分析歌词...")

        try:
            from src.ai.ai_manager import ai_video_generator
            import time

            # 获取歌词
            lyrics_content = self._get_lyrics_content()
            if not lyrics_content:
                self.logger.error("无法获取歌词内容")
                return []

            self._emit_progress("AI生成", 70.0, "生成AI视频...")

            # 生成AI视频
            temp_video_path = f"./temp/ai_video_{int(time.time())}.mp4"
            success = ai_video_generator.generate_video_from_lyrics(
                lyrics_content,
                temp_video_path,
                self.task_config.analysis_model,
                self.task_config.image_model
            )

            if success:
                self._emit_progress("AI生成", 80.0, "AI视频生成完成")
                return [temp_video_path]
            else:
                self.logger.error("AI视频生成失败")
                return []

        except Exception as e:
            self.logger.error(f"AI视频生成异常: {e}")
            return []

    def _download_traditional_videos(self) -> List[str]:
        """下载传统视频素材"""
        self._emit_progress("视频素材", 65.0, "搜索视频素材...")

        # 构建搜索关键词
        keywords = [self.task_config.song_title, self.task_config.artist]
        keywords.extend(self.task_config.keywords)

        # 搜索视频
        search_results = video_manager.search_videos(
            keywords,
            self.task_config.video_count,
            self.task_config.video_source
        )

        if not search_results:
            return []

        self._emit_progress("视频素材", 70.0, f"下载{len(search_results)}个视频素材...")

        # 下载视频
        video_urls = [result['url'] for result in search_results]
        video_files = video_manager.download_videos(
            video_urls, self.task_config.video_source
        )

        if video_files:
            self._emit_progress("视频素材", 80.0, f"视频素材下载完成，共{len(video_files)}个文件")

        return video_files

    def _get_lyrics_content(self) -> str:
        """获取歌词内容"""
        try:
            # 从字幕管理器获取歌词
            lyrics = subtitle_manager.get_lyrics(
                self.task_config.song_title,
                self.task_config.artist,
                self.task_config.lyric_provider
            )

            if lyrics:
                return lyrics
            else:
                # 如果没有找到歌词，使用默认内容
                return f"歌曲: {self.task_config.song_title}\n演唱: {self.task_config.artist}"

        except Exception as e:
            self.logger.error(f"获取歌词失败: {e}")
            return f"歌曲: {self.task_config.song_title}\n演唱: {self.task_config.artist}"
    
    def _compose_video(self, audio_info: AudioInfo, video_files: List[str], 
                      subtitle_path: Optional[str]) -> bool:
        """合成视频"""
        self._emit_progress("视频合成", 85.0, "开始合成视频...")
        
        # 确保输出目录存在
        output_path = Path(self.task_config.output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 合成视频
        success = video_manager.create_music_video(
            video_files,
            audio_info.file_path,
            subtitle_path or "",
            self.task_config.output_path,
            audio_info.duration
        )
        
        if success:
            self._emit_progress("视频合成", 95.0, "视频合成完成")
        
        return success
    
    def _emit_progress(self, stage: str, progress: float, message: str):
        """发送进度更新"""
        task_progress = TaskProgress(
            stage=stage,
            progress=progress,
            message=message,
            completed=progress >= 100.0
        )
        self.progress_updated.emit(task_progress)
    
    def _emit_error(self, error_message: str):
        """发送错误信息"""
        task_progress = TaskProgress(
            stage="错误",
            progress=0.0,
            message="",
            error=error_message,
            completed=True
        )
        self.progress_updated.emit(task_progress)
        self.task_completed.emit(False, error_message)


class MusicVideoController(QObject):
    """音乐视频控制器"""
    
    # 进度更新信号
    progress_updated = Signal(TaskProgress)
    # 任务完成信号
    task_completed = Signal(bool, str)
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger("MusicVideoController")
        self.current_worker = None
        self.current_task = None
    
    def start_generation(self, task_config: TaskConfig) -> bool:
        """
        开始生成音乐视频
        
        Args:
            task_config: 任务配置
            
        Returns:
            bool: 是否成功启动任务
        """
        if self.current_worker and self.current_worker.isRunning():
            self.logger.warning("已有任务正在运行，请先停止当前任务")
            return False
        
        # 验证配置
        if not self._validate_config(task_config):
            return False
        
        self.current_task = task_config
        
        # 创建工作线程
        self.current_worker = VideoGenerationWorker(task_config)
        self.current_worker.progress_updated.connect(self.progress_updated.emit)
        self.current_worker.task_completed.connect(self._on_task_completed)
        
        # 启动任务
        self.current_worker.start()
        
        self.logger.info(f"开始生成音乐视频: {task_config.song_title} - {task_config.artist}")
        return True
    
    def stop_generation(self) -> None:
        """停止当前生成任务"""
        if self.current_worker and self.current_worker.isRunning():
            self.current_worker.stop()
            self.current_worker.wait(5000)  # 等待5秒
            if self.current_worker.isRunning():
                self.current_worker.terminate()
            
            self.logger.info("音乐视频生成任务已停止")
    
    def is_running(self) -> bool:
        """检查是否有任务正在运行"""
        return self.current_worker and self.current_worker.isRunning()
    
    def get_current_task(self) -> Optional[TaskConfig]:
        """获取当前任务配置"""
        return self.current_task
    
    def _validate_config(self, config: TaskConfig) -> bool:
        """验证任务配置"""
        if not config.song_title.strip():
            self.logger.error("歌曲标题不能为空")
            return False
        
        if not config.output_path.strip():
            self.logger.error("输出路径不能为空")
            return False
        
        # 检查输出目录是否可写
        output_dir = Path(config.output_path).parent
        try:
            output_dir.mkdir(parents=True, exist_ok=True)
        except Exception as e:
            self.logger.error(f"无法创建输出目录: {e}")
            return False
        
        return True
    
    def _on_task_completed(self, success: bool, result: str):
        """任务完成回调"""
        if success:
            self.logger.info(f"音乐视频生成成功: {result}")
        else:
            self.logger.error(f"音乐视频生成失败: {result}")
        
        self.task_completed.emit(success, result)
        self.current_task = None
    
    def cleanup_temp_files(self) -> None:
        """清理临时文件"""
        try:
            audio_manager.cleanup_temp_files()
            subtitle_manager.cleanup_temp_files()
            video_manager.cleanup_temp_files()
            self.logger.info("临时文件清理完成")
        except Exception as e:
            self.logger.error(f"清理临时文件失败: {e}")


# 全局控制器实例
controller = MusicVideoController()
