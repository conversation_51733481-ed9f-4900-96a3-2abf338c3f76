"""
主题管理模块
负责管理应用程序的全局主题设置，包括颜色方案、字体、样式等UI外观配置
支持深色主题、浅色主题和自定义主题
"""

from typing import Dict, Any, Optional
from dataclasses import dataclass
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QObject, Signal
from PySide6.QtGui import QPalette, QColor, QFont


@dataclass
class ColorScheme:
    """颜色方案类"""
    primary: str = "#2196F3"
    secondary: str = "#FFC107"
    background: str = "#FFFFFF"
    surface: str = "#F5F5F5"
    text_primary: str = "#212121"
    text_secondary: str = "#757575"
    accent: str = "#FF5722"
    success: str = "#4CAF50"
    warning: str = "#FF9800"
    error: str = "#F44336"
    border: str = "#E0E0E0"
    hover: str = "#E3F2FD"


@dataclass
class FontConfig:
    """字体配置类"""
    family: str = "Microsoft YaHei"
    size: int = 10
    weight: str = "normal"  # normal, bold
    
    
class ThemeManager(QObject):
    """主题管理器"""
    
    # 主题变更信号
    theme_changed = Signal(str)
    
    def __init__(self):
        super().__init__()

        # 只保留浅色主题
        self.themes = {
            "light": self._create_light_theme()
        }

        self.current_theme = "light"
        self.current_colors = self.themes[self.current_theme]["colors"]
        self.current_fonts = self.themes[self.current_theme]["fonts"]
    
    def _create_light_theme(self) -> Dict[str, Any]:
        """创建浅色主题"""
        return {
            "name": "浅色主题",
            "colors": ColorScheme(
                primary="#1976D2",
                secondary="#FF9800",
                background="#FFFFFF",
                surface="#F8F9FA",
                text_primary="#1A1A1A",
                text_secondary="#424242",
                accent="#D32F2F",
                success="#388E3C",
                warning="#F57C00",
                error="#C62828",
                border="#CCCCCC",
                hover="#E3F2FD"
            ),
            "fonts": FontConfig(
                family="Microsoft YaHei",
                size=10,
                weight="normal"
            )
        }
    

    
    def set_theme(self, theme_name: str = "light") -> bool:
        """
        设置主题（固定为浅色主题）

        Args:
            theme_name: 主题名称（忽略，始终使用light）

        Returns:
            bool: 设置是否成功
        """
        self.current_theme = "light"
        self.current_colors = self.themes["light"]["colors"]
        self.current_fonts = self.themes["light"]["fonts"]

        # 应用主题到应用程序
        self._apply_theme()

        # 发送主题变更信号
        self.theme_changed.emit("light")

        return True
    
    def _apply_theme(self) -> None:
        """应用主题到应用程序"""
        app = QApplication.instance()
        if app is None:
            return
        
        # 设置应用程序样式表
        stylesheet = self._generate_stylesheet()
        app.setStyleSheet(stylesheet)
        
        # 设置调色板
        palette = self._create_palette()
        app.setPalette(palette)
    
    def _generate_stylesheet(self) -> str:
        """生成样式表"""
        colors = self.current_colors
        fonts = self.current_fonts
        
        return f"""
        /* 主窗口样式 */
        QMainWindow {{
            background-color: {colors.background};
            color: {colors.text_primary};
            font-family: {fonts.family};
            font-size: {fonts.size}pt;
        }}

        /* 按钮样式 */
        QPushButton {{
            background-color: {colors.primary};
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 9pt;
        }}

        QPushButton:hover {{
            background-color: #1565C0;
            color: white;
        }}

        QPushButton:pressed {{
            background-color: {colors.accent};
            color: white;
        }}

        QPushButton:disabled {{
            background-color: #CCCCCC;
            color: #666666;
        }}

        /* 输入框样式 */
        QLineEdit, QTextEdit, QPlainTextEdit {{
            background-color: white;
            color: {colors.text_primary};
            border: 2px solid {colors.border};
            padding: 6px;
            border-radius: 4px;
            font-size: 9pt;
        }}

        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {{
            border-color: {colors.primary};
            background-color: white;
        }}

        /* 标签样式 */
        QLabel {{
            color: {colors.text_primary};
            font-size: 9pt;
        }}

        /* 进度条样式 */
        QProgressBar {{
            border: 2px solid {colors.border};
            border-radius: 4px;
            text-align: center;
            background-color: white;
            color: {colors.text_primary};
            font-weight: bold;
        }}

        QProgressBar::chunk {{
            background-color: {colors.primary};
            border-radius: 2px;
        }}

        /* 分组框样式 */
        QGroupBox {{
            font-weight: bold;
            color: {colors.text_primary};
            border: 2px solid {colors.border};
            border-radius: 5px;
            margin-top: 10px;
            padding-top: 10px;
            font-size: 9pt;
        }}

        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: {colors.text_primary};
            font-weight: bold;
        }}

        /* 下拉框样式 */
        QComboBox {{
            background-color: white;
            color: {colors.text_primary};
            border: 2px solid {colors.border};
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 9pt;
        }}

        QComboBox:focus {{
            border-color: {colors.primary};
        }}

        QComboBox::drop-down {{
            border: none;
        }}

        QComboBox::down-arrow {{
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid {colors.text_primary};
        }}

        /* 数字输入框样式 */
        QSpinBox {{
            background-color: white;
            color: {colors.text_primary};
            border: 2px solid {colors.border};
            padding: 4px;
            border-radius: 4px;
            font-size: 9pt;
        }}

        QSpinBox:focus {{
            border-color: {colors.primary};
        }}

        /* 列表样式 */
        QListWidget {{
            background-color: white;
            color: {colors.text_primary};
            border: 2px solid {colors.border};
            border-radius: 4px;
        }}

        QListWidget::item {{
            padding: 5px;
            border-bottom: 1px solid {colors.border};
            color: {colors.text_primary};
        }}

        QListWidget::item:selected {{
            background-color: {colors.primary};
            color: white;
        }}

        QListWidget::item:hover {{
            background-color: {colors.hover};
            color: {colors.text_primary};
        }}

        /* 菜单样式 */
        QMenuBar {{
            background-color: {colors.surface};
            color: {colors.text_primary};
            font-size: 9pt;
        }}

        QMenuBar::item {{
            color: {colors.text_primary};
            padding: 4px 8px;
        }}

        QMenuBar::item:selected {{
            background-color: {colors.primary};
            color: white;
        }}

        QMenu {{
            background-color: white;
            color: {colors.text_primary};
            border: 2px solid {colors.border};
        }}

        QMenu::item {{
            color: {colors.text_primary};
            padding: 4px 20px;
        }}

        QMenu::item:selected {{
            background-color: {colors.primary};
            color: white;
        }}

        /* 状态栏样式 */
        QStatusBar {{
            background-color: {colors.surface};
            color: {colors.text_primary};
            border-top: 2px solid {colors.border};
            font-size: 9pt;
        }}

        /* 对话框样式 */
        QDialog {{
            background-color: {colors.background};
            color: {colors.text_primary};
        }}

        /* 选项卡样式 */
        QTabWidget::pane {{
            border: 2px solid {colors.border};
            background-color: white;
        }}

        QTabBar::tab {{
            background-color: {colors.surface};
            color: {colors.text_primary};
            padding: 8px 16px;
            border: 1px solid {colors.border};
            font-size: 9pt;
        }}

        QTabBar::tab:selected {{
            background-color: white;
            color: {colors.text_primary};
            font-weight: bold;
        }}

        QTabBar::tab:hover {{
            background-color: {colors.hover};
        }}
        """
    
    def _create_palette(self) -> QPalette:
        """创建调色板"""
        palette = QPalette()
        colors = self.current_colors
        
        # 设置各种颜色角色
        palette.setColor(QPalette.Window, QColor(colors.background))
        palette.setColor(QPalette.WindowText, QColor(colors.text_primary))
        palette.setColor(QPalette.Base, QColor(colors.surface))
        palette.setColor(QPalette.AlternateBase, QColor(colors.hover))
        palette.setColor(QPalette.Text, QColor(colors.text_primary))
        palette.setColor(QPalette.Button, QColor(colors.primary))
        palette.setColor(QPalette.ButtonText, QColor("white"))
        palette.setColor(QPalette.Highlight, QColor(colors.primary))
        palette.setColor(QPalette.HighlightedText, QColor("white"))
        
        return palette
    
    def get_color(self, color_name: str) -> str:
        """获取颜色值"""
        return getattr(self.current_colors, color_name, "#000000")
    
    def get_font(self) -> QFont:
        """获取当前字体"""
        font = QFont(self.current_fonts.family, self.current_fonts.size)
        if self.current_fonts.weight == "bold":
            font.setBold(True)
        return font
    
    def get_available_themes(self) -> list:
        """获取可用主题列表"""
        return ["light"]

    def get_current_theme(self) -> str:
        """获取当前主题名称"""
        return "light"


# 全局主题管理器实例
theme_manager = ThemeManager()
