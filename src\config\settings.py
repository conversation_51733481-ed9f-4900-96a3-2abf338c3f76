"""
配置管理模块
负责管理应用程序的所有配置信息，包括API密钥、文件路径、用户偏好设置等
支持从配置文件加载和保存用户设置
"""

import os
import yaml
import json
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict


@dataclass
class AudioConfig:
    """音频配置类"""
    quality: str = "high"  # high, medium, low
    format: str = "mp3"
    bitrate: int = 320
    sample_rate: int = 44100


@dataclass
class VideoConfig:
    """视频配置类"""
    resolution: str = "1080p"  # 1080p, 720p, 480p
    fps: int = 30
    format: str = "mp4"
    codec: str = "h264"
    quality: str = "high"


@dataclass
class SubtitleConfig:
    """字幕配置类"""
    font_size: int = 24
    font_family: str = "Microsoft YaHei"
    font_color: str = "#FFFFFF"
    outline_color: str = "#000000"
    position: str = "bottom"  # top, center, bottom


@dataclass
class APIConfig:
    """API配置类"""
    pexels_api_key: str = ""
    pixabay_api_key: str = ""
    netease_api_url: str = ""
    qq_music_api_url: str = ""


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件目录，默认为用户目录下的.ai_music
        """
        if config_dir is None:
            self.config_dir = Path.home() / ".ai_music"
        else:
            self.config_dir = Path(config_dir)
        
        self.config_dir.mkdir(exist_ok=True)
        self.config_file = self.config_dir / "config.yaml"
        
        # 默认配置
        self.audio = AudioConfig()
        self.video = VideoConfig()
        self.subtitle = SubtitleConfig()
        self.api = APIConfig()
        
        # 应用程序配置
        self.app_config = {
            "temp_dir": str(self.config_dir / "temp"),
            "output_dir": str(self.config_dir / "output"),
            "cache_dir": str(self.config_dir / "cache"),
            "log_level": "INFO",
            "max_concurrent_downloads": 5,
            "max_video_duration": 300,  # 5分钟
            "auto_cleanup": True,
            "theme": "light",
            "ffmpeg_path": self._get_ffmpeg_path()
        }
        
        self.load_config()
    
    def load_config(self) -> None:
        """从配置文件加载配置"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)
                
                if config_data:
                    # 加载各模块配置
                    if 'audio' in config_data:
                        self.audio = AudioConfig(**config_data['audio'])
                    if 'video' in config_data:
                        self.video = VideoConfig(**config_data['video'])
                    if 'subtitle' in config_data:
                        self.subtitle = SubtitleConfig(**config_data['subtitle'])
                    if 'api' in config_data:
                        self.api = APIConfig(**config_data['api'])
                    if 'app' in config_data:
                        self.app_config.update(config_data['app'])
                        
            except Exception as e:
                print(f"加载配置文件失败: {e}")
        
        # 确保目录存在
        self._ensure_directories()
    
    def save_config(self) -> None:
        """保存配置到文件"""
        config_data = {
            'audio': asdict(self.audio),
            'video': asdict(self.video),
            'subtitle': asdict(self.subtitle),
            'api': asdict(self.api),
            'app': self.app_config
        }
        
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config_data, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def _ensure_directories(self) -> None:
        """确保必要的目录存在"""
        for dir_key in ['temp_dir', 'output_dir', 'cache_dir']:
            dir_path = Path(self.app_config[dir_key])
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def get_temp_dir(self) -> Path:
        """获取临时目录"""
        return Path(self.app_config['temp_dir'])
    
    def get_output_dir(self) -> Path:
        """获取输出目录"""
        return Path(self.app_config['output_dir'])
    
    def get_cache_dir(self) -> Path:
        """获取缓存目录"""
        return Path(self.app_config['cache_dir'])
    
    def update_api_key(self, service: str, api_key: str) -> None:
        """更新API密钥"""
        if hasattr(self.api, f"{service}_api_key"):
            setattr(self.api, f"{service}_api_key", api_key)
            self.save_config()
    
    def get_api_key(self, service: str) -> str:
        """获取API密钥"""
        return getattr(self.api, f"{service}_api_key", "")

    def _get_ffmpeg_path(self) -> str:
        """获取FFmpeg可执行文件路径"""
        import os
        from pathlib import Path

        # 首先检查项目bin目录
        project_root = Path(__file__).parent.parent.parent
        bin_dir = project_root / "src" / "bin"
        ffmpeg_exe = bin_dir / "ffmpeg.exe"

        if ffmpeg_exe.exists():
            return str(ffmpeg_exe)

        # 检查系统PATH
        import shutil
        system_ffmpeg = shutil.which("ffmpeg")
        if system_ffmpeg:
            return system_ffmpeg

        # 返回默认值
        return "ffmpeg"

    def get_ffmpeg_path(self) -> str:
        """获取FFmpeg路径"""
        return self.app_config.get("ffmpeg_path", "ffmpeg")


# 全局配置实例
config = ConfigManager()
