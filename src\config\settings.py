"""
配置管理模块
负责管理应用程序的所有配置信息，包括API密钥、文件路径、用户偏好设置等
支持从配置文件加载和保存用户设置
"""

import os
import yaml
import json
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict


@dataclass
class AudioConfig:
    """音频配置类"""
    quality: str = "high"  # high, medium, low
    format: str = "mp3"
    codec: str = "aac"
    bitrate: int = 192  # kbps
    sample_rate: int = 44100
    channels: int = 2  # 1=mono, 2=stereo


@dataclass
class VideoConfig:
    """视频配置类"""
    resolution: str = "1080p"  # 1080p, 720p, 480p
    width: int = 1920
    height: int = 1080
    fps: int = 30
    format: str = "mp4"
    codec: str = "libx264"
    preset: str = "fast"
    bitrate: int = 3000  # kbps
    quality: str = "high"


@dataclass
class SubtitleConfig:
    """字幕配置类"""
    # 基本字体设置
    font_size: int = 24
    font_family: str = "Microsoft YaHei"
    font_color: str = "#FFFFFF"
    font_weight: str = "normal"  # normal, bold

    # 描边和阴影
    outline_color: str = "#000000"
    outline_width: int = 2
    shadow: int = 1
    shadow_color: str = "#808080"

    # 位置和对齐
    position: str = "bottom"  # top, center, bottom
    alignment: int = 2  # 1=左, 2=中, 3=右 (ASS格式)
    margin_v: int = 50  # 垂直边距
    margin_h: int = 20  # 水平边距

    # 显示模式
    display_mode: str = "static"  # static=静态显示, scroll=滚动显示, karaoke=卡拉OK
    scroll_speed: float = 1.0  # 滚动速度倍数
    scroll_direction: str = "up"  # up=向上, down=向下, left=向左, right=向右

    # 高级设置
    background_alpha: float = 0.0  # 背景透明度 0.0-1.0
    background_color: str = "#000000"
    line_spacing: float = 1.2  # 行间距倍数
    max_lines: int = 2  # 最大显示行数


@dataclass
class WatermarkConfig:
    """水印配置类"""
    # 基本设置
    enabled: bool = False
    text: str = "AI Music Video"
    font_size: int = 20
    font_family: str = "Arial"
    font_color: str = "#FFFFFF"

    # 描边和透明度
    outline_color: str = "#000000"
    outline_width: int = 1
    opacity: float = 0.7  # 透明度 0.0-1.0

    # 位置设置
    position: str = "bottom_right"  # top_left, top_right, bottom_left, bottom_right, center
    margin_x: int = 20  # 水平边距
    margin_y: int = 20  # 垂直边距

    # 移动设置
    movement_enabled: bool = False
    movement_type: str = "linear"  # linear=直线, circular=圆形, bounce=弹跳, random=随机
    movement_speed: float = 1.0  # 移动速度倍数
    movement_range: int = 100  # 移动范围(像素)

    # 高级设置
    rotation_enabled: bool = False
    rotation_speed: float = 1.0  # 旋转速度(度/秒)
    scale_animation: bool = False
    scale_range: float = 0.2  # 缩放范围 0.0-1.0


@dataclass
class APIConfig:
    """API配置类"""
    pexels_api_key: str = ""
    pixabay_api_key: str = ""
    netease_api_url: str = ""
    qq_music_api_url: str = ""


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件目录，默认为用户目录下的.ai_music
        """
        if config_dir is None:
            self.config_dir = Path.home() / ".ai_music"
        else:
            self.config_dir = Path(config_dir)
        
        self.config_dir.mkdir(exist_ok=True)
        self.config_file = self.config_dir / "config.yaml"
        
        # 默认配置
        self.audio = AudioConfig()
        self.video = VideoConfig()
        self.subtitle = SubtitleConfig()
        self.watermark = WatermarkConfig()
        self.api = APIConfig()
        
        # 应用程序配置
        self.app_config = {
            "temp_dir": str(self.config_dir / "temp"),
            "output_dir": str(self.config_dir / "output"),
            "cache_dir": str(self.config_dir / "cache"),
            "log_level": "INFO",
            "max_concurrent_downloads": 5,
            "max_video_duration": 300,  # 5分钟
            "auto_cleanup": True,
            "theme": "light",
            "ffmpeg_path": self._get_ffmpeg_path(),
            # 视频处理参数
            "max_clip_duration": 30,  # 视频片段最大长度(秒)
            "min_clip_duration": 3,   # 视频片段最小长度(秒)
            "scale_mode": 0,          # 缩放模式: 0=保持比例填充黑边, 1=保持比例裁剪, 2=拉伸
            # 音频处理参数
            "audio_volume": 100,      # 音量百分比
            "fade_in_duration": 1,    # 淡入时间(秒)
            "fade_out_duration": 2,   # 淡出时间(秒)
            # 抽帧参数
            "frame_extract_enabled": False,  # 是否启用抽帧
            "frame_extract_interval": 5.0,   # 抽帧间隔(秒)
            "frame_extract_count": 3,        # 每次抽帧数量
            "frame_extract_quality": "high", # 抽帧质量: low, medium, high
            # AI功能参数
            "enable_ai_generation": False,   # 启用AI图片生成
        }

        # AI配置
        self.ai_config = {
            "analysis_model": "gemini",      # 歌词分析模型
            "image_model": "siliconflow",    # 图片生成模型
            "gemini_api_key": "",
            "zhipu_api_key": "",
            "deepseek_api_key": "",
            "siliconflow_api_key": "",
            "doubao_api_key": "",
            "image_style": "realistic",      # 图片风格
            "image_size": "1024x1024",       # 图片尺寸
            "scene_duration": 3.0,           # 每个场景默认时长
        }
        
        self.load_config()
    
    def load_config(self) -> None:
        """从配置文件加载配置"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)
                
                if config_data:
                    # 加载各模块配置
                    if 'audio' in config_data:
                        self.audio = AudioConfig(**config_data['audio'])
                    if 'video' in config_data:
                        self.video = VideoConfig(**config_data['video'])
                    if 'subtitle' in config_data:
                        self.subtitle = SubtitleConfig(**config_data['subtitle'])
                    if 'api' in config_data:
                        self.api = APIConfig(**config_data['api'])
                    if 'app' in config_data:
                        self.app_config.update(config_data['app'])
                        
            except Exception as e:
                print(f"加载配置文件失败: {e}")
        
        # 确保目录存在
        self._ensure_directories()
    
    def save_config(self) -> None:
        """保存配置到文件"""
        config_data = {
            'audio': asdict(self.audio),
            'video': asdict(self.video),
            'subtitle': asdict(self.subtitle),
            'api': asdict(self.api),
            'app': self.app_config
        }
        
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config_data, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def _ensure_directories(self) -> None:
        """确保必要的目录存在"""
        for dir_key in ['temp_dir', 'output_dir', 'cache_dir']:
            dir_path = Path(self.app_config[dir_key])
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def get_temp_dir(self) -> Path:
        """获取临时目录"""
        return Path(self.app_config['temp_dir'])
    
    def get_output_dir(self) -> Path:
        """获取输出目录"""
        return Path(self.app_config['output_dir'])
    
    def get_cache_dir(self) -> Path:
        """获取缓存目录"""
        return Path(self.app_config['cache_dir'])
    
    def update_api_key(self, service: str, api_key: str) -> None:
        """更新API密钥"""
        if hasattr(self.api, f"{service}_api_key"):
            setattr(self.api, f"{service}_api_key", api_key)
            self.save_config()
    
    def get_api_key(self, service: str) -> str:
        """获取API密钥"""
        return getattr(self.api, f"{service}_api_key", "")

    def _get_ffmpeg_path(self) -> str:
        """获取FFmpeg可执行文件路径"""
        import os
        from pathlib import Path

        # 首先检查项目bin目录
        project_root = Path(__file__).parent.parent.parent
        bin_dir = project_root / "src" / "bin"
        ffmpeg_exe = bin_dir / "ffmpeg.exe"

        if ffmpeg_exe.exists():
            return str(ffmpeg_exe)

        # 检查系统PATH
        import shutil
        system_ffmpeg = shutil.which("ffmpeg")
        if system_ffmpeg:
            return system_ffmpeg

        # 返回默认值
        return "ffmpeg"

    def get_ffmpeg_path(self) -> str:
        """获取FFmpeg路径"""
        return self.app_config.get("ffmpeg_path", "ffmpeg")


# 全局配置实例
config = ConfigManager()
