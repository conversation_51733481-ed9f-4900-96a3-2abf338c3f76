"""
音频管理模块
负责音频文件的获取、下载、处理和元数据提取，支持多种音乐平台API
包括网易云音乐、QQ音乐、YouTube等平台的音频获取功能
"""

import os
import requests
import yt_dlp
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from mutagen import File as MutagenFile
from mutagen.id3 import ID3, TIT2, TPE1, TALB, TDRC

from src.logger.logger import get_logger
from src.config.settings import config


@dataclass
class AudioInfo:
    """音频信息类"""
    title: str = ""
    artist: str = ""
    album: str = ""
    duration: int = 0  # 秒
    bitrate: int = 0
    file_path: str = ""
    file_size: int = 0
    format: str = "mp3"


class AudioDownloader:
    """音频下载器基类"""
    
    def __init__(self):
        self.logger = get_logger("AudioDownloader")
    
    def search(self, query: str, artist: str = "") -> List[Dict[str, Any]]:
        """
        搜索音频
        
        Args:
            query: 搜索关键词
            artist: 艺术家名称
            
        Returns:
            List[Dict]: 搜索结果列表
        """
        raise NotImplementedError
    
    def download(self, url: str, output_path: str) -> Optional[AudioInfo]:
        """
        下载音频
        
        Args:
            url: 音频URL
            output_path: 输出路径
            
        Returns:
            AudioInfo: 音频信息，下载失败返回None
        """
        raise NotImplementedError


class YouTubeDownloader(AudioDownloader):
    """YouTube音频下载器"""
    
    def __init__(self):
        super().__init__()
        self.ydl_opts = {
            'format': 'bestaudio/best',
            'extractaudio': True,
            'audioformat': 'mp3',
            'outtmpl': '%(title)s.%(ext)s',
            'restrictfilenames': True,
            'noplaylist': True,
            'nocheckcertificate': True,
            'ignoreerrors': False,
            'logtostderr': False,
            'quiet': True,
            'no_warnings': True,
        }
    
    def search(self, query: str, artist: str = "") -> List[Dict[str, Any]]:
        """搜索YouTube音频"""
        search_query = f"{query} {artist}".strip()
        
        try:
            with yt_dlp.YoutubeDL(self.ydl_opts) as ydl:
                search_results = ydl.extract_info(
                    f"ytsearch5:{search_query}",
                    download=False
                )
                
                results = []
                if 'entries' in search_results:
                    for entry in search_results['entries']:
                        results.append({
                            'id': entry.get('id', ''),
                            'title': entry.get('title', ''),
                            'uploader': entry.get('uploader', ''),
                            'duration': entry.get('duration', 0),
                            'url': entry.get('webpage_url', ''),
                            'thumbnail': entry.get('thumbnail', '')
                        })
                
                return results
                
        except Exception as e:
            self.logger.error(f"YouTube搜索失败: {e}")
            return []
    
    def download(self, url: str, output_path: str) -> Optional[AudioInfo]:
        """下载YouTube音频"""
        try:
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # 设置输出模板
            self.ydl_opts['outtmpl'] = str(output_path)
            
            with yt_dlp.YoutubeDL(self.ydl_opts) as ydl:
                # 获取视频信息
                info = ydl.extract_info(url, download=False)
                
                # 下载音频
                ydl.download([url])
                
                # 创建音频信息
                audio_info = AudioInfo(
                    title=info.get('title', ''),
                    artist=info.get('uploader', ''),
                    duration=info.get('duration', 0),
                    file_path=output_path,
                    format='mp3'
                )
                
                # 获取文件大小
                if os.path.exists(output_path):
                    audio_info.file_size = os.path.getsize(output_path)
                
                self.logger.info(f"YouTube音频下载成功: {audio_info.title}")
                return audio_info
                
        except Exception as e:
            self.logger.error(f"YouTube音频下载失败: {e}")
            return None


class NeteaseMusicDownloader(AudioDownloader):
    """网易云音乐下载器"""

    def __init__(self):
        super().__init__()
        try:
            import pyncm
            from pyncm import apis
            self.pyncm = pyncm
            self.apis = apis
            self.available = True
        except ImportError:
            self.logger.error("pyncm库未安装，请运行: pip install pyncm")
            self.available = False

    def search(self, query: str, artist: str = "") -> List[Dict[str, Any]]:
        """搜索网易云音乐"""
        if not self.available:
            return []

        try:
            search_query = f"{query} {artist}".strip()

            # 搜索歌曲
            result = self.apis.cloudsearch.GetSearchResult(search_query, stype=1, limit=10)

            songs = []
            if 'result' in result and 'songs' in result['result']:
                for song in result['result']['songs']:
                    songs.append({
                        'id': song.get('id'),
                        'title': song.get('name', ''),
                        'artist': ', '.join([ar.get('name', '') for ar in song.get('ar', [])]),
                        'album': song.get('al', {}).get('name', ''),
                        'duration': song.get('dt', 0) // 1000,  # 转换为秒
                        'url': f"netease://{song.get('id')}"
                    })

            self.logger.info(f"网易云音乐搜索到{len(songs)}首歌曲")
            return songs

        except Exception as e:
            self.logger.error(f"网易云音乐搜索失败: {e}")
            return []

    def download(self, url: str, output_path: str) -> Optional[AudioInfo]:
        """下载网易云音乐"""
        if not self.available:
            return None

        try:
            # 从URL中提取歌曲ID
            if url.startswith("netease://"):
                song_id = int(url.replace("netease://", ""))
            else:
                self.logger.error("无效的网易云音乐URL")
                return None

            # 获取歌曲详情
            song_detail = self.apis.track.GetTrackDetail(song_id)
            if not song_detail or 'songs' not in song_detail or not song_detail['songs']:
                self.logger.error("无法获取歌曲详情")
                return None

            song_info = song_detail['songs'][0]

            # 获取音频URL，尝试不同的音质
            audio_url = None
            bitrates = [320000, 192000, 128000]  # 尝试不同音质

            for bitrate in bitrates:
                try:
                    audio_result = self.apis.track.GetTrackAudio(song_id, bitrate=bitrate)
                    if audio_result and 'data' in audio_result and audio_result['data']:
                        audio_url = audio_result['data'][0].get('url')
                        if audio_url:
                            self.logger.info(f"获取到音频链接，音质: {bitrate}kbps")
                            break
                except Exception as e:
                    self.logger.warning(f"尝试获取{bitrate}kbps音质失败: {e}")
                    continue

            if not audio_url:
                self.logger.error("无法获取任何音质的音频下载链接，可能是版权保护或需要登录")
                # 创建一个占位符音频信息，提示用户手动添加音频文件
            self.logger.warning("由于版权限制，无法自动下载音频。请手动提供音频文件。")
            return None

            # 下载音频文件
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)

            response = requests.get(audio_url, stream=True)
            response.raise_for_status()

            with open(output_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            # 创建音频信息
            audio_info = AudioInfo(
                title=song_info.get('name', ''),
                artist=', '.join([ar.get('name', '') for ar in song_info.get('ar', [])]),
                album=song_info.get('al', {}).get('name', ''),
                duration=song_info.get('dt', 0) // 1000,
                file_path=output_path,
                file_size=os.path.getsize(output_path),
                format='mp3'
            )

            self.logger.info(f"网易云音乐下载成功: {audio_info.title}")
            return audio_info

        except Exception as e:
            self.logger.error(f"网易云音乐下载失败: {e}")
            return None


class LocalAudioDownloader(AudioDownloader):
    """本地音频文件下载器"""

    def search(self, query: str, artist: str = "") -> List[Dict[str, Any]]:
        """搜索本地音频文件"""
        # 检查常见的音频文件位置
        common_paths = [
            Path.home() / "Music",
            Path.home() / "Downloads",
            Path("./audio"),
            Path("./music")
        ]

        results = []
        search_terms = [query.lower(), artist.lower()]

        for base_path in common_paths:
            if not base_path.exists():
                continue

            for ext in ['.mp3', '.wav', '.flac', '.m4a']:
                for audio_file in base_path.glob(f"**/*{ext}"):
                    filename = audio_file.name.lower()

                    # 检查文件名是否包含搜索词
                    if any(term in filename for term in search_terms if term):
                        try:
                            file_size = audio_file.stat().st_size
                            results.append({
                                'id': str(audio_file),
                                'title': audio_file.stem,
                                'artist': artist or "Unknown",
                                'album': "Local File",
                                'duration': 0,  # 需要时可以用mutagen获取
                                'url': f"file://{audio_file}",
                                'file_size': file_size
                            })
                        except Exception:
                            continue

        self.logger.info(f"找到{len(results)}个本地音频文件")
        return results[:10]  # 最多返回10个结果

    def download(self, url: str, output_path: str) -> Optional[AudioInfo]:
        """复制本地音频文件"""
        try:
            if not url.startswith("file://"):
                return None

            source_path = Path(url.replace("file://", ""))
            if not source_path.exists():
                self.logger.error(f"本地音频文件不存在: {source_path}")
                return None

            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)

            # 复制文件
            import shutil
            shutil.copy2(source_path, output_path)

            # 创建音频信息
            audio_info = AudioInfo(
                title=source_path.stem,
                artist="Local File",
                album="Local Collection",
                duration=0,
                file_path=output_path,
                file_size=os.path.getsize(output_path),
                format=source_path.suffix[1:]  # 去掉点号
            )

            # 尝试提取元数据
            try:
                audio_file = MutagenFile(output_path)
                if audio_file and hasattr(audio_file, 'info'):
                    audio_info.duration = int(audio_file.info.length)
            except Exception:
                pass

            self.logger.info(f"本地音频文件复制成功: {audio_info.title}")
            return audio_info

        except Exception as e:
            self.logger.error(f"本地音频文件处理失败: {e}")
            return None


class AudioManager:
    """音频管理器"""
    
    def __init__(self):
        self.logger = get_logger("AudioManager")
        self.downloaders = {
            'youtube': YouTubeDownloader(),
            'netease': NeteaseMusicDownloader(),
            'local': LocalAudioDownloader()
        }
        self.temp_dir = config.get_temp_dir() / "audio"
        self.temp_dir.mkdir(parents=True, exist_ok=True)
    
    def search_audio(self, query: str, artist: str = "", 
                    source: str = "youtube") -> List[Dict[str, Any]]:
        """
        搜索音频
        
        Args:
            query: 搜索关键词
            artist: 艺术家名称
            source: 音频源
            
        Returns:
            List[Dict]: 搜索结果
        """
        if source not in self.downloaders:
            self.logger.error(f"不支持的音频源: {source}")
            return []
        
        self.logger.info(f"搜索音频: {query} - {artist} (来源: {source})")
        return self.downloaders[source].search(query, artist)
    
    def download_audio(self, url: str, filename: str = None, 
                      source: str = "youtube") -> Optional[AudioInfo]:
        """
        下载音频
        
        Args:
            url: 音频URL
            filename: 文件名
            source: 音频源
            
        Returns:
            AudioInfo: 音频信息
        """
        if source not in self.downloaders:
            self.logger.error(f"不支持的音频源: {source}")
            return None
        
        if filename is None:
            filename = f"audio_{hash(url)}.mp3"
        
        output_path = self.temp_dir / filename
        
        self.logger.info(f"开始下载音频: {url}")
        audio_info = self.downloaders[source].download(url, str(output_path))
        
        if audio_info:
            # 提取音频元数据
            self._extract_metadata(audio_info)
        
        return audio_info
    
    def _extract_metadata(self, audio_info: AudioInfo) -> None:
        """提取音频元数据"""
        try:
            if not os.path.exists(audio_info.file_path):
                return
            
            audio_file = MutagenFile(audio_info.file_path)
            if audio_file is None:
                return
            
            # 更新音频信息
            if hasattr(audio_file, 'info'):
                audio_info.duration = int(audio_file.info.length)
                audio_info.bitrate = getattr(audio_file.info, 'bitrate', 0)
            
            # 提取标签信息
            if hasattr(audio_file, 'tags') and audio_file.tags:
                tags = audio_file.tags
                
                # 尝试不同的标签格式
                title_keys = ['TIT2', 'TITLE', '\xa9nam']
                artist_keys = ['TPE1', 'ARTIST', '\xa9ART']
                album_keys = ['TALB', 'ALBUM', '\xa9alb']
                
                for key in title_keys:
                    if key in tags:
                        audio_info.title = str(tags[key][0])
                        break
                
                for key in artist_keys:
                    if key in tags:
                        audio_info.artist = str(tags[key][0])
                        break
                
                for key in album_keys:
                    if key in tags:
                        audio_info.album = str(tags[key][0])
                        break
            
            self.logger.debug(f"音频元数据提取完成: {audio_info.title}")
            
        except Exception as e:
            self.logger.error(f"提取音频元数据失败: {e}")
    
    def get_audio_info(self, file_path: str) -> Optional[AudioInfo]:
        """
        获取音频文件信息
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            AudioInfo: 音频信息
        """
        if not os.path.exists(file_path):
            return None
        
        audio_info = AudioInfo(file_path=file_path)
        audio_info.file_size = os.path.getsize(file_path)
        
        self._extract_metadata(audio_info)
        return audio_info
    
    def cleanup_temp_files(self) -> None:
        """清理临时文件"""
        try:
            for file_path in self.temp_dir.glob("*"):
                if file_path.is_file():
                    file_path.unlink()
            self.logger.info("音频临时文件清理完成")
        except Exception as e:
            self.logger.error(f"清理音频临时文件失败: {e}")


# 全局音频管理器实例
audio_manager = AudioManager()
