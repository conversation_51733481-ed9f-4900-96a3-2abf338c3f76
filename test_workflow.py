#!/usr/bin/env python3
"""
工作流程测试脚本
测试完整的音乐视频生成流程
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_audio_search():
    """测试音频搜索功能"""
    print("测试音频搜索功能...")
    try:
        from src.audio.audio_manager import audio_manager
        
        # 测试网易云音乐搜索
        results = audio_manager.search_audio("起风了", "吴青峰", "netease")
        print(f"✓ 网易云音乐搜索成功，找到{len(results)}首歌曲")
        
        if results:
            first_song = results[0]
            print(f"  - 第一首歌: {first_song.get('title')} - {first_song.get('artist')}")
            print(f"  - 时长: {first_song.get('duration')}秒")
        
        return True
    except Exception as e:
        print(f"✗ 音频搜索测试失败: {e}")
        return False

def test_lyrics_search():
    """测试歌词搜索功能"""
    print("\n测试歌词搜索功能...")
    try:
        from src.subtitle.subtitle_manager import subtitle_manager
        
        # 测试网易云歌词获取
        lyrics = subtitle_manager.get_lyrics("起风了", "吴青峰", "netease")
        
        if lyrics:
            print("✓ 网易云歌词获取成功")
            lines = lyrics.split('\n')[:3]  # 显示前3行
            for line in lines:
                if line.strip():
                    print(f"  - {line.strip()}")
            print("  - ...")
        else:
            print("⚠ 未获取到歌词（可能需要网络连接）")
        
        return True
    except Exception as e:
        print(f"✗ 歌词搜索测试失败: {e}")
        return False

def test_video_search():
    """测试视频搜索功能"""
    print("\n测试视频搜索功能...")
    try:
        from src.video.video_manager import video_manager
        from src.config.settings import config
        
        # 检查是否有Pexels API密钥
        api_key = config.get_api_key("pexels")
        if not api_key:
            print("⚠ 未设置Pexels API密钥，跳过视频搜索测试")
            print("  请在程序中设置API密钥：设置 → API设置")
            return True
        
        # 测试Pexels视频搜索
        results = video_manager.search_videos(["nature", "wind"], 3, "pexels")
        print(f"✓ Pexels视频搜索成功，找到{len(results)}个视频")
        
        if results:
            first_video = results[0]
            print(f"  - 第一个视频: {first_video.get('title')}")
            print(f"  - 尺寸: {first_video.get('width')}x{first_video.get('height')}")
            print(f"  - 时长: {first_video.get('duration')}秒")
        
        return True
    except Exception as e:
        print(f"✗ 视频搜索测试失败: {e}")
        return False

def test_ffmpeg():
    """测试FFmpeg功能"""
    print("\n测试FFmpeg功能...")
    try:
        from src.video.video_manager import VideoProcessor
        import tempfile
        import os
        
        processor = VideoProcessor()
        
        # 测试FFmpeg路径
        ffmpeg_path = processor._get_ffmpeg_path()
        print(f"✓ FFmpeg路径: {ffmpeg_path}")
        
        # 检查FFmpeg是否可执行
        if os.path.exists(ffmpeg_path):
            print("✓ FFmpeg文件存在")
        else:
            print("⚠ FFmpeg文件不存在")
        
        return True
    except Exception as e:
        print(f"✗ FFmpeg测试失败: {e}")
        return False

def test_subtitle_generation():
    """测试字幕生成功能"""
    print("\n测试字幕生成功能...")
    try:
        from src.subtitle.subtitle_manager import subtitle_manager
        import tempfile
        import os
        
        # 测试LRC歌词解析
        test_lrc = """[00:00.00]测试歌曲
[00:05.00]这是第一行歌词
[00:10.00]这是第二行歌词
[00:15.00]这是第三行歌词"""
        
        with tempfile.NamedTemporaryFile(suffix='.srt', delete=False) as f:
            temp_path = f.name
        
        success = subtitle_manager.create_subtitle(test_lrc, 20.0, temp_path, "srt")
        
        if success and os.path.exists(temp_path):
            print("✓ 字幕文件生成成功")
            
            # 读取生成的字幕文件
            with open(temp_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')[:6]  # 显示前6行
                for line in lines:
                    if line.strip():
                        print(f"  - {line.strip()}")
            
            # 清理临时文件
            os.unlink(temp_path)
        else:
            print("⚠ 字幕文件生成失败")
        
        return True
    except Exception as e:
        print(f"✗ 字幕生成测试失败: {e}")
        return False

def test_configuration():
    """测试配置功能"""
    print("\n测试配置功能...")
    try:
        from src.config.settings import config
        
        print(f"✓ 配置目录: {config.config_dir}")
        print(f"✓ 临时目录: {config.get_temp_dir()}")
        print(f"✓ 输出目录: {config.get_output_dir()}")
        print(f"✓ FFmpeg路径: {config.get_ffmpeg_path()}")
        
        # 测试API密钥
        pexels_key = config.get_api_key("pexels")
        if pexels_key:
            print(f"✓ Pexels API密钥已设置 (长度: {len(pexels_key)})")
        else:
            print("⚠ Pexels API密钥未设置")
        
        return True
    except Exception as e:
        print(f"✗ 配置测试失败: {e}")
        return False

def main():
    """主函数"""
    print("AI音乐视频生成器 - 工作流程测试")
    print("=" * 50)
    
    tests = [
        test_configuration,
        test_ffmpeg,
        test_audio_search,
        test_lyrics_search,
        test_subtitle_generation,
        test_video_search
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有工作流程测试通过！")
        print("\n🎉 程序已准备就绪，可以开始使用！")
        print("\n使用建议：")
        print("1. 如果要使用视频功能，请设置Pexels API密钥")
        print("2. 在程序中点击：设置 → API设置")
        print("3. 访问 https://www.pexels.com/api/ 获取免费API密钥")
        return 0
    else:
        print("⚠ 部分功能可能需要额外配置")
        return 1

if __name__ == "__main__":
    sys.exit(main())
