#!/usr/bin/env python3
"""
音频元数据测试脚本
测试音频文件的元数据提取功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_audio_metadata():
    """测试音频元数据提取"""
    print("测试音频元数据提取...")
    
    try:
        from src.audio.audio_manager import audio_manager
        
        # 测试本地音频文件
        audio_dir = Path("./audio")
        if not audio_dir.exists():
            print("⚠ audio目录不存在，请先运行 create_test_audio.py")
            return False
        
        # 查找测试音频文件
        test_files = list(audio_dir.glob("*.mp3"))
        if not test_files:
            print("⚠ 未找到测试音频文件，请先运行 create_test_audio.py")
            return False
        
        print(f"找到{len(test_files)}个音频文件:")
        
        for audio_file in test_files:
            print(f"\n测试文件: {audio_file.name}")
            print(f"文件大小: {audio_file.stat().st_size} 字节")
            
            # 获取音频信息
            audio_info = audio_manager.get_audio_info(str(audio_file))
            
            if audio_info:
                print("✓ 音频信息提取成功:")
                print(f"  - 标题: {audio_info.title}")
                print(f"  - 艺术家: {audio_info.artist}")
                print(f"  - 专辑: {audio_info.album}")
                print(f"  - 时长: {audio_info.duration} 秒")
                print(f"  - 比特率: {audio_info.bitrate} kbps")
                print(f"  - 格式: {audio_info.format}")
            else:
                print("✗ 音频信息提取失败")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ 测试异常: {e}")
        return False

def test_local_audio_search():
    """测试本地音频搜索"""
    print("\n测试本地音频搜索...")
    
    try:
        from src.audio.audio_manager import audio_manager
        
        # 搜索测试音频
        results = audio_manager.search_audio("测试歌曲", "测试歌手", "local")
        
        if results:
            print(f"✓ 本地音频搜索成功，找到{len(results)}个文件:")
            for i, result in enumerate(results):
                print(f"  {i+1}. {result.get('title')} - {result.get('artist')}")
                print(f"     文件: {result.get('url')}")
        else:
            print("⚠ 未找到匹配的本地音频文件")
            print("请确保:")
            print("1. 音频文件存在于 audio/ 目录")
            print("2. 文件名包含搜索关键词")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 搜索测试异常: {e}")
        return False

def test_local_audio_download():
    """测试本地音频下载（复制）"""
    print("\n测试本地音频下载...")
    
    try:
        from src.audio.audio_manager import audio_manager
        import tempfile
        import os
        
        # 搜索测试音频
        results = audio_manager.search_audio("测试歌曲", "测试歌手", "local")
        
        if not results:
            print("⚠ 未找到测试音频文件")
            return False
        
        # 下载第一个结果
        first_result = results[0]
        audio_url = first_result.get('url')
        
        # 创建临时输出文件
        with tempfile.NamedTemporaryFile(suffix='.mp3', delete=False) as f:
            temp_path = f.name
        
        print(f"下载音频: {audio_url}")
        print(f"输出路径: {temp_path}")
        
        audio_info = audio_manager.download_audio(audio_url, temp_path, "local")
        
        if audio_info:
            print("✓ 本地音频下载成功:")
            print(f"  - 标题: {audio_info.title}")
            print(f"  - 艺术家: {audio_info.artist}")
            print(f"  - 文件路径: {audio_info.file_path}")
            print(f"  - 文件大小: {audio_info.file_size} 字节")
            
            # 清理临时文件
            if os.path.exists(temp_path):
                os.unlink(temp_path)
                print("  - 临时文件已清理")
        else:
            print("✗ 本地音频下载失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 下载测试异常: {e}")
        return False

def test_mutagen_directly():
    """直接测试mutagen库"""
    print("\n测试mutagen库...")
    
    try:
        from mutagen import File as MutagenFile
        
        audio_dir = Path("./audio")
        test_files = list(audio_dir.glob("*.mp3"))
        
        if not test_files:
            print("⚠ 未找到测试音频文件")
            return False
        
        test_file = test_files[0]
        print(f"测试文件: {test_file}")
        
        audio_file = MutagenFile(str(test_file))
        
        if audio_file is None:
            print("✗ mutagen无法识别文件格式")
            return False
        
        print("✓ mutagen识别成功:")
        print(f"  - 文件类型: {type(audio_file).__name__}")
        
        if hasattr(audio_file, 'info') and audio_file.info:
            print(f"  - 时长: {audio_file.info.length:.2f} 秒")
            print(f"  - 比特率: {getattr(audio_file.info, 'bitrate', 'N/A')} bps")
        
        if hasattr(audio_file, 'tags') and audio_file.tags:
            print("  - 标签信息:")
            for key, value in audio_file.tags.items():
                print(f"    {key}: {value}")
        else:
            print("  - 无标签信息")
        
        return True
        
    except Exception as e:
        print(f"✗ mutagen测试失败: {e}")
        return False

def main():
    """主函数"""
    print("AI音乐视频生成器 - 音频元数据测试")
    print("=" * 50)
    
    tests = [
        test_mutagen_directly,
        test_audio_metadata,
        test_local_audio_search,
        test_local_audio_download
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有音频测试通过！")
        return 0
    else:
        print("⚠ 部分测试失败，请检查音频文件")
        return 1

if __name__ == "__main__":
    sys.exit(main())
