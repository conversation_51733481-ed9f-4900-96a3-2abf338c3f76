"""
字幕管理模块
负责歌词获取、字幕文件生成和时间轴处理，支持LRC和SRT格式转换
包括从各种音乐平台获取歌词和生成视频字幕文件
"""

import re
import requests
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import pysrt

from src.logger.logger import get_logger
from src.config.settings import config


@dataclass
class LyricLine:
    """歌词行类"""
    time: float  # 时间戳（秒）
    text: str    # 歌词文本
    duration: float = 0.0  # 持续时间


class LyricParser:
    """歌词解析器"""
    
    def __init__(self):
        self.logger = get_logger("LyricParser")
    
    def parse_lrc(self, lrc_content: str) -> List[LyricLine]:
        """
        解析LRC格式歌词
        
        Args:
            lrc_content: LRC歌词内容
            
        Returns:
            List[LyricLine]: 歌词行列表
        """
        lyrics = []
        
        # LRC时间戳正则表达式
        time_pattern = r'\[(\d{2}):(\d{2})\.(\d{2,3})\]'
        
        lines = lrc_content.strip().split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 查找时间戳
            time_matches = re.findall(time_pattern, line)
            if not time_matches:
                continue
            
            # 提取歌词文本
            text = re.sub(time_pattern, '', line).strip()
            if not text:
                continue
            
            # 处理多个时间戳的情况
            for match in time_matches:
                minutes, seconds, milliseconds = match
                
                # 转换为总秒数
                total_seconds = (
                    int(minutes) * 60 + 
                    int(seconds) + 
                    int(milliseconds.ljust(3, '0')) / 1000
                )
                
                lyrics.append(LyricLine(time=total_seconds, text=text))
        
        # 按时间排序
        lyrics.sort(key=lambda x: x.time)
        
        # 计算每行歌词的持续时间
        for i in range(len(lyrics) - 1):
            lyrics[i].duration = lyrics[i + 1].time - lyrics[i].time
        
        # 最后一行默认持续3秒
        if lyrics:
            lyrics[-1].duration = 3.0
        
        self.logger.info(f"解析LRC歌词完成，共{len(lyrics)}行")
        return lyrics
    
    def parse_plain_text(self, text_content: str, audio_duration: float) -> List[LyricLine]:
        """
        解析纯文本歌词（无时间戳）
        
        Args:
            text_content: 纯文本歌词
            audio_duration: 音频总时长
            
        Returns:
            List[LyricLine]: 歌词行列表
        """
        lyrics = []
        lines = [line.strip() for line in text_content.strip().split('\n') if line.strip()]
        
        if not lines:
            return lyrics
        
        # 平均分配时间
        time_per_line = audio_duration / len(lines)
        
        for i, line in enumerate(lines):
            start_time = i * time_per_line
            lyrics.append(LyricLine(
                time=start_time,
                text=line,
                duration=time_per_line
            ))
        
        self.logger.info(f"解析纯文本歌词完成，共{len(lyrics)}行")
        return lyrics


class SubtitleGenerator:
    """字幕生成器"""
    
    def __init__(self):
        self.logger = get_logger("SubtitleGenerator")
    
    def generate_srt(self, lyrics: List[LyricLine], output_path: str) -> bool:
        """
        生成SRT字幕文件
        
        Args:
            lyrics: 歌词行列表
            output_path: 输出文件路径
            
        Returns:
            bool: 生成是否成功
        """
        try:
            subs = pysrt.SubRipFile()
            
            for i, lyric in enumerate(lyrics):
                start_time = pysrt.SubRipTime(seconds=lyric.time)
                end_time = pysrt.SubRipTime(seconds=lyric.time + lyric.duration)
                
                subtitle = pysrt.SubRipItem(
                    index=i + 1,
                    start=start_time,
                    end=end_time,
                    text=lyric.text
                )
                
                subs.append(subtitle)
            
            # 保存文件
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)
            
            subs.save(output_path, encoding='utf-8')
            
            self.logger.info(f"SRT字幕文件生成成功: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"生成SRT字幕文件失败: {e}")
            return False
    
    def generate_ass(self, lyrics: List[LyricLine], output_path: str) -> bool:
        """
        生成ASS字幕文件
        
        Args:
            lyrics: 歌词行列表
            output_path: 输出文件路径
            
        Returns:
            bool: 生成是否成功
        """
        try:
            # ASS文件头部
            ass_header = """[Script Info]
Title: AI Music Video Subtitle
ScriptType: v4.00+

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,Microsoft YaHei,24,&H00FFFFFF,&H000000FF,&H00000000,&H80000000,1,0,0,0,100,100,0,0,1,2,0,2,10,10,10,1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
"""
            
            # 生成事件
            events = []
            for lyric in lyrics:
                start_time = self._seconds_to_ass_time(lyric.time)
                end_time = self._seconds_to_ass_time(lyric.time + lyric.duration)
                
                event = f"Dialogue: 0,{start_time},{end_time},Default,,0,0,0,,{lyric.text}"
                events.append(event)
            
            # 写入文件
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(ass_header)
                f.write('\n'.join(events))
            
            self.logger.info(f"ASS字幕文件生成成功: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"生成ASS字幕文件失败: {e}")
            return False
    
    def _seconds_to_ass_time(self, seconds: float) -> str:
        """将秒数转换为ASS时间格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        centiseconds = int((seconds % 1) * 100)
        
        return f"{hours}:{minutes:02d}:{secs:02d}.{centiseconds:02d}"


class NeteaseLyricProvider:
    """网易云音乐歌词提供者"""

    def __init__(self):
        self.logger = get_logger("NeteaseLyricProvider")
        try:
            import pyncm
            from pyncm import apis
            self.pyncm = pyncm
            self.apis = apis
            self.available = True
        except ImportError:
            self.logger.error("pyncm库未安装，无法获取网易云歌词")
            self.available = False

    def search_lyrics(self, title: str, artist: str) -> Optional[str]:
        """搜索网易云音乐歌词"""
        if not self.available:
            return None

        try:
            # 搜索歌曲
            search_query = f"{title} {artist}".strip()
            result = self.apis.cloudsearch.GetSearchResult(search_query, stype=1, limit=5)

            if 'result' not in result or 'songs' not in result['result']:
                self.logger.warning(f"未找到歌曲: {title} - {artist}")
                return None

            # 获取第一首歌的歌词
            song = result['result']['songs'][0]
            song_id = song.get('id')

            if not song_id:
                return None

            # 获取歌词
            lyric_result = self.apis.track.GetTrackLyrics(song_id)

            if 'lrc' in lyric_result and 'lyric' in lyric_result['lrc']:
                lyrics = lyric_result['lrc']['lyric']
                self.logger.info(f"获取网易云歌词成功: {title} - {artist}")
                return lyrics

            self.logger.warning(f"未找到歌词: {title} - {artist}")
            return None

        except Exception as e:
            self.logger.error(f"获取网易云歌词失败: {e}")
            return None


class QQLyricProvider:
    """QQ音乐歌词提供者"""

    def __init__(self):
        self.logger = get_logger("QQLyricProvider")

    def search_lyrics(self, title: str, artist: str) -> Optional[str]:
        """搜索QQ音乐歌词"""
        try:
            # QQ音乐搜索API
            search_url = "https://c.y.qq.com/soso/fcgi-bin/client_search_cp"
            search_params = {
                'ct': 24,
                'qqmusic_ver': 1298,
                'new_json': 1,
                'remoteplace': 'txt.yqq.song',
                'searchid': 71600207,
                't': 0,
                'aggr': 1,
                'cr': 1,
                'catZhida': 1,
                'lossless': 0,
                'flag_qc': 0,
                'p': 1,
                'n': 5,
                'w': f"{title} {artist}".strip(),
                'g_tk': 5381,
                'loginUin': 0,
                'hostUin': 0,
                'format': 'json',
                'inCharset': 'utf8',
                'outCharset': 'utf-8',
                'notice': 0,
                'platform': 'yqq.json',
                'needNewCode': 0
            }

            headers = {
                'Referer': 'https://y.qq.com/',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }

            response = requests.get(search_url, params=search_params, headers=headers)
            response.raise_for_status()

            data = response.json()

            if 'data' not in data or 'song' not in data['data'] or 'list' not in data['data']['song']:
                return None

            songs = data['data']['song']['list']
            if not songs:
                return None

            # 获取第一首歌的歌词
            song = songs[0]
            song_mid = song.get('mid')

            if not song_mid:
                return None

            # 获取歌词
            lyric_url = "https://c.y.qq.com/lyric/fcgi-bin/fcg_query_lyric_new.fcg"
            lyric_params = {
                'callback': 'MusicJsonCallback_lrc',
                'pcachetime': 1,
                'songmid': song_mid,
                'g_tk': 5381,
                'jsonpCallback': 'MusicJsonCallback_lrc',
                'loginUin': 0,
                'hostUin': 0,
                'format': 'jsonp',
                'inCharset': 'utf8',
                'outCharset': 'utf-8',
                'notice': 0,
                'platform': 'yqq.json',
                'needNewCode': 0
            }

            lyric_response = requests.get(lyric_url, params=lyric_params, headers=headers)
            lyric_response.raise_for_status()

            # 解析JSONP响应
            lyric_text = lyric_response.text
            if lyric_text.startswith('MusicJsonCallback_lrc(') and lyric_text.endswith(')'):
                import json
                json_str = lyric_text[22:-1]  # 去掉JSONP包装
                lyric_data = json.loads(json_str)

                if 'lyric' in lyric_data:
                    import base64
                    lyrics = base64.b64decode(lyric_data['lyric']).decode('utf-8')
                    self.logger.info(f"获取QQ音乐歌词成功: {title} - {artist}")
                    return lyrics

            return None

        except Exception as e:
            self.logger.error(f"获取QQ音乐歌词失败: {e}")
            return None


class SubtitleManager:
    """字幕管理器"""
    
    def __init__(self):
        self.logger = get_logger("SubtitleManager")
        self.parser = LyricParser()
        self.generator = SubtitleGenerator()
        self.providers = {
            'netease': NeteaseLyricProvider(),
            'qq': QQLyricProvider()
        }
        self.temp_dir = config.get_temp_dir() / "subtitle"
        self.temp_dir.mkdir(parents=True, exist_ok=True)
    
    def get_lyrics(self, title: str, artist: str, 
                  provider: str = "mock") -> Optional[str]:
        """
        获取歌词
        
        Args:
            title: 歌曲标题
            artist: 艺术家
            provider: 歌词提供者
            
        Returns:
            str: 歌词内容
        """
        if provider not in self.providers:
            self.logger.error(f"不支持的歌词提供者: {provider}")
            return None
        
        self.logger.info(f"获取歌词: {title} - {artist} (提供者: {provider})")
        return self.providers[provider].search_lyrics(title, artist)
    
    def create_subtitle(self, lyrics_content: str, audio_duration: float,
                       output_path: str, subtitle_format: str = "srt") -> bool:
        """
        创建字幕文件
        
        Args:
            lyrics_content: 歌词内容
            audio_duration: 音频时长
            output_path: 输出路径
            subtitle_format: 字幕格式 (srt, ass)
            
        Returns:
            bool: 创建是否成功
        """
        try:
            # 解析歌词
            if '[' in lyrics_content and ']' in lyrics_content:
                # LRC格式
                lyrics = self.parser.parse_lrc(lyrics_content)
            else:
                # 纯文本格式
                lyrics = self.parser.parse_plain_text(lyrics_content, audio_duration)
            
            if not lyrics:
                self.logger.error("歌词解析失败，无法创建字幕")
                return False
            
            # 生成字幕文件
            if subtitle_format.lower() == "srt":
                return self.generator.generate_srt(lyrics, output_path)
            elif subtitle_format.lower() == "ass":
                return self.generator.generate_ass(lyrics, output_path)
            else:
                self.logger.error(f"不支持的字幕格式: {subtitle_format}")
                return False
                
        except Exception as e:
            self.logger.error(f"创建字幕文件失败: {e}")
            return False
    
    def get_subtitle_info(self, subtitle_path: str) -> Dict[str, Any]:
        """
        获取字幕文件信息
        
        Args:
            subtitle_path: 字幕文件路径
            
        Returns:
            Dict: 字幕信息
        """
        info = {
            'path': subtitle_path,
            'exists': False,
            'line_count': 0,
            'duration': 0.0,
            'format': 'unknown'
        }
        
        try:
            if not Path(subtitle_path).exists():
                return info
            
            info['exists'] = True
            
            # 判断格式
            if subtitle_path.lower().endswith('.srt'):
                info['format'] = 'srt'
                subs = pysrt.open(subtitle_path, encoding='utf-8')
                info['line_count'] = len(subs)
                if subs:
                    info['duration'] = subs[-1].end.ordinal / 1000.0
            elif subtitle_path.lower().endswith('.ass'):
                info['format'] = 'ass'
                with open(subtitle_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    info['line_count'] = content.count('Dialogue:')
            
        except Exception as e:
            self.logger.error(f"获取字幕信息失败: {e}")
        
        return info
    
    def cleanup_temp_files(self) -> None:
        """清理临时文件"""
        try:
            for file_path in self.temp_dir.glob("*"):
                if file_path.is_file():
                    file_path.unlink()
            self.logger.info("字幕临时文件清理完成")
        except Exception as e:
            self.logger.error(f"清理字幕临时文件失败: {e}")


# 全局字幕管理器实例
subtitle_manager = SubtitleManager()
