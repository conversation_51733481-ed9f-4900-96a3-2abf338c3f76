"""
AI分析和图片生成管理器
支持多种AI模型进行歌词分析和图片生成
"""

import os
import json
import time
import requests
from typing import List, Dict, Any, Optional
from pathlib import Path

from src.logger.logger import get_logger
from src.config.settings import config


class LyricAnalyzer:
    """歌词分析器 - 使用AI模型分析歌词并生成分镜描述"""
    
    def __init__(self):
        self.logger = get_logger("LyricAnalyzer")
        self.supported_models = {
            'gemini': self._analyze_with_gemini,
            'zhipu': self._analyze_with_zhipu,
            'deepseek': self._analyze_with_deepseek
        }
    
    def analyze_lyrics(self, lyrics: str, model: str = 'gemini') -> List[Dict[str, Any]]:
        """
        分析歌词并生成分镜描述
        
        Args:
            lyrics: 歌词文本
            model: AI模型名称
            
        Returns:
            List[Dict]: 分镜描述列表
        """
        try:
            if model not in self.supported_models:
                self.logger.error(f"不支持的AI模型: {model}")
                return []
            
            self.logger.info(f"使用{model}模型分析歌词...")
            return self.supported_models[model](lyrics)
            
        except Exception as e:
            self.logger.error(f"歌词分析失败: {e}")
            return []
    
    def _analyze_with_gemini(self, lyrics: str) -> List[Dict[str, Any]]:
        """使用Gemini分析歌词"""
        try:
            api_key = config.ai_config.get('gemini_api_key', '')
            if not api_key:
                self.logger.error("Gemini API密钥未配置")
                return []
            
            prompt = self._build_analysis_prompt(lyrics)
            
            headers = {
                'Content-Type': 'application/json',
            }
            
            data = {
                'contents': [{
                    'parts': [{'text': prompt}]
                }],
                'generationConfig': {
                    'temperature': 0.7,
                    'maxOutputTokens': 2048
                }
            }
            
            url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key={api_key}"
            
            response = requests.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            content = result['candidates'][0]['content']['parts'][0]['text']
            
            return self._parse_analysis_result(content)
            
        except Exception as e:
            self.logger.error(f"Gemini分析失败: {e}")
            return []
    
    def _analyze_with_zhipu(self, lyrics: str) -> List[Dict[str, Any]]:
        """使用智谱AI分析歌词"""
        try:
            api_key = config.ai_config.get('zhipu_api_key', '')
            if not api_key:
                self.logger.error("智谱AI API密钥未配置")
                return []
            
            prompt = self._build_analysis_prompt(lyrics)
            
            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json',
            }
            
            data = {
                'model': 'glm-4',
                'messages': [
                    {'role': 'user', 'content': prompt}
                ],
                'temperature': 0.7,
                'max_tokens': 2048
            }
            
            url = "https://open.bigmodel.cn/api/paas/v4/chat/completions"
            
            response = requests.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            content = result['choices'][0]['message']['content']
            
            return self._parse_analysis_result(content)
            
        except Exception as e:
            self.logger.error(f"智谱AI分析失败: {e}")
            return []
    
    def _analyze_with_deepseek(self, lyrics: str) -> List[Dict[str, Any]]:
        """使用DeepSeek分析歌词"""
        try:
            api_key = config.ai_config.get('deepseek_api_key', '')
            if not api_key:
                self.logger.error("DeepSeek API密钥未配置")
                return []
            
            prompt = self._build_analysis_prompt(lyrics)
            
            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json',
            }
            
            data = {
                'model': 'deepseek-chat',
                'messages': [
                    {'role': 'user', 'content': prompt}
                ],
                'temperature': 0.7,
                'max_tokens': 2048
            }
            
            url = "https://api.deepseek.com/chat/completions"
            
            response = requests.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            content = result['choices'][0]['message']['content']
            
            return self._parse_analysis_result(content)
            
        except Exception as e:
            self.logger.error(f"DeepSeek分析失败: {e}")
            return []
    
    def _build_analysis_prompt(self, lyrics: str) -> str:
        """构建分析提示词"""
        return f"""
请分析以下歌词，为每一句歌词生成对应的画面描述，用于AI绘图。

歌词：
{lyrics}

请按照以下JSON格式返回结果：
{{
    "scenes": [
        {{
            "lyric": "歌词内容",
            "description": "详细的画面描述，包含场景、人物、情感、色彩、构图等元素",
            "style": "艺术风格，如写实、动漫、油画等",
            "mood": "情感氛围，如温暖、忧郁、激昂等",
            "duration": 3.0
        }}
    ]
}}

要求：
1. 每句歌词对应一个场景
2. 描述要具体生动，适合AI绘图
3. 考虑歌词的情感和意境
4. 保持整体风格的一致性
5. duration为该场景的持续时间（秒）
"""
    
    def _parse_analysis_result(self, content: str) -> List[Dict[str, Any]]:
        """解析分析结果"""
        try:
            # 提取JSON部分
            start = content.find('{')
            end = content.rfind('}') + 1
            
            if start == -1 or end == 0:
                self.logger.error("未找到有效的JSON格式")
                return []
            
            json_str = content[start:end]
            result = json.loads(json_str)
            
            scenes = result.get('scenes', [])
            self.logger.info(f"解析到{len(scenes)}个场景")
            
            return scenes
            
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON解析失败: {e}")
            return []
        except Exception as e:
            self.logger.error(f"结果解析失败: {e}")
            return []


class ImageGenerator:
    """AI图片生成器"""
    
    def __init__(self):
        self.logger = get_logger("ImageGenerator")
        self.supported_models = {
            'gemini': self._generate_with_gemini,
            'zhipu': self._generate_with_zhipu,
            'siliconflow': self._generate_with_siliconflow,
            'doubao': self._generate_with_doubao
        }
    
    def generate_image(self, description: str, model: str = 'siliconflow', 
                      style: str = '', size: str = '1024x1024') -> Optional[str]:
        """
        生成图片
        
        Args:
            description: 图片描述
            model: AI模型名称
            style: 艺术风格
            size: 图片尺寸
            
        Returns:
            str: 生成的图片路径
        """
        try:
            if model not in self.supported_models:
                self.logger.error(f"不支持的图片生成模型: {model}")
                return None
            
            self.logger.info(f"使用{model}模型生成图片...")
            return self.supported_models[model](description, style, size)
            
        except Exception as e:
            self.logger.error(f"图片生成失败: {e}")
            return None
    
    def _generate_with_siliconflow(self, description: str, style: str, size: str) -> Optional[str]:
        """使用SiliconFlow生成图片"""
        try:
            api_key = config.ai_config.get('siliconflow_api_key', '')
            if not api_key:
                self.logger.error("SiliconFlow API密钥未配置")
                return None
            
            # 构建完整的提示词
            full_prompt = f"{description}"
            if style:
                full_prompt += f", {style} style"
            
            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json',
            }
            
            data = {
                'model': 'black-forest-labs/FLUX.1-schnell',
                'prompt': full_prompt,
                'image_size': size,
                'batch_size': 1,
                'num_inference_steps': 4,
                'guidance_scale': 7.5
            }
            
            url = "https://api.siliconflow.cn/v1/images/generations"
            
            response = requests.post(url, headers=headers, json=data, timeout=60)
            response.raise_for_status()
            
            result = response.json()
            image_url = result['data'][0]['url']
            
            # 下载图片
            return self._download_image(image_url)
            
        except Exception as e:
            self.logger.error(f"SiliconFlow图片生成失败: {e}")
            return None
    
    def _generate_with_doubao(self, description: str, style: str, size: str) -> Optional[str]:
        """使用豆包生成图片"""
        try:
            api_key = config.ai_config.get('doubao_api_key', '')
            if not api_key:
                self.logger.error("豆包API密钥未配置")
                return None
            
            # 豆包图片生成API实现
            # 这里需要根据豆包的实际API文档实现
            self.logger.warning("豆包图片生成功能待实现")
            return None
            
        except Exception as e:
            self.logger.error(f"豆包图片生成失败: {e}")
            return None
    
    def _download_image(self, image_url: str) -> Optional[str]:
        """下载图片到本地"""
        try:
            import uuid
            
            # 创建图片目录
            images_dir = Path("./temp/images")
            images_dir.mkdir(parents=True, exist_ok=True)
            
            # 生成唯一文件名
            filename = f"ai_image_{uuid.uuid4().hex[:8]}.png"
            file_path = images_dir / filename
            
            # 下载图片
            response = requests.get(image_url, timeout=30)
            response.raise_for_status()
            
            with open(file_path, 'wb') as f:
                f.write(response.content)
            
            self.logger.info(f"图片下载成功: {file_path}")
            return str(file_path)
            
        except Exception as e:
            self.logger.error(f"图片下载失败: {e}")
            return None


class AIVideoGenerator:
    """AI视频生成器 - 整合歌词分析和图片生成"""
    
    def __init__(self):
        self.logger = get_logger("AIVideoGenerator")
        self.analyzer = LyricAnalyzer()
        self.generator = ImageGenerator()
    
    def generate_video_from_lyrics(self, lyrics: str, output_path: str,
                                 analysis_model: str = 'gemini',
                                 image_model: str = 'siliconflow') -> bool:
        """
        从歌词生成AI视频
        
        Args:
            lyrics: 歌词内容
            output_path: 输出视频路径
            analysis_model: 歌词分析模型
            image_model: 图片生成模型
            
        Returns:
            bool: 生成是否成功
        """
        try:
            # 1. 分析歌词
            scenes = self.analyzer.analyze_lyrics(lyrics, analysis_model)
            if not scenes:
                self.logger.error("歌词分析失败")
                return False
            
            # 2. 生成图片
            image_paths = []
            for i, scene in enumerate(scenes):
                self.logger.info(f"生成第{i+1}/{len(scenes)}张图片...")
                
                image_path = self.generator.generate_image(
                    scene['description'],
                    image_model,
                    scene.get('style', ''),
                    '1024x1024'
                )
                
                if image_path:
                    image_paths.append({
                        'path': image_path,
                        'duration': scene.get('duration', 3.0),
                        'lyric': scene.get('lyric', '')
                    })
                else:
                    self.logger.warning(f"第{i+1}张图片生成失败")
            
            if not image_paths:
                self.logger.error("没有成功生成任何图片")
                return False
            
            # 3. 合成视频
            return self._create_video_from_images(image_paths, output_path)
            
        except Exception as e:
            self.logger.error(f"AI视频生成失败: {e}")
            return False
    
    def _create_video_from_images(self, image_data: List[Dict], output_path: str) -> bool:
        """从图片创建视频"""
        try:
            import ffmpeg
            from src.video.video_manager import video_manager
            
            # 创建临时视频片段
            temp_videos = []
            temp_dir = Path("./temp/video_segments")
            temp_dir.mkdir(parents=True, exist_ok=True)
            
            for i, data in enumerate(image_data):
                segment_path = temp_dir / f"segment_{i:03d}.mp4"
                
                # 使用FFmpeg将图片转换为视频片段
                stream = (
                    ffmpeg
                    .input(data['path'], loop=1, t=data['duration'])
                    .filter('scale', 1920, 1080)
                    .output(str(segment_path),
                           vcodec='libx264',
                           pix_fmt='yuv420p',
                           r=30)
                    .overwrite_output()
                )
                
                if video_manager.processor._run_ffmpeg_command(stream, f"图片转视频{i+1}"):
                    temp_videos.append(str(segment_path))
                else:
                    self.logger.warning(f"图片{i+1}转视频失败")
            
            if not temp_videos:
                self.logger.error("没有成功创建任何视频片段")
                return False
            
            # 拼接视频片段
            success = video_manager.processor.concatenate_videos(temp_videos, output_path)
            
            # 清理临时文件
            import shutil
            shutil.rmtree(temp_dir, ignore_errors=True)
            
            return success
            
        except Exception as e:
            self.logger.error(f"视频合成失败: {e}")
            return False


# 全局实例
ai_video_generator = AIVideoGenerator()
