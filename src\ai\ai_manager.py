"""
AI分析和图片生成管理器
支持多种AI模型进行歌词分析和图片生成
"""

import os
import json
import time
import requests
from typing import List, Dict, Any, Optional
from pathlib import Path

from src.logger.logger import get_logger
from src.config.settings import config


class LyricAnalyzer:
    """歌词分析器 - 使用AI模型分析歌词并生成分镜描述"""
    
    def __init__(self):
        self.logger = get_logger("LyricAnalyzer")
        self.supported_models = {
            'gemini': self._analyze_with_gemini,
            'zhipu': self._analyze_with_zhipu,
            'deepseek': self._analyze_with_deepseek
        }
    
    def analyze_lyrics(self, lyrics: str, model: str = 'gemini') -> List[Dict[str, Any]]:
        """
        分析歌词并生成分镜描述
        
        Args:
            lyrics: 歌词文本
            model: AI模型名称
            
        Returns:
            List[Dict]: 分镜描述列表
        """
        try:
            if model not in self.supported_models:
                self.logger.error(f"不支持的AI模型: {model}")
                return []
            
            self.logger.info(f"使用{model}模型分析歌词...")
            return self.supported_models[model](lyrics)
            
        except Exception as e:
            self.logger.error(f"歌词分析失败: {e}")
            return []
    
    def _analyze_with_gemini(self, lyrics: str) -> List[Dict[str, Any]]:
        """使用Gemini分析歌词"""
        try:
            model_config = config.text_models.get('gemini', {})
            api_key = model_config.get('api_key', '')
            if not api_key:
                self.logger.error("Gemini API密钥未配置")
                return []

            # 使用Google GenAI SDK
            try:
                from google import genai
                from google.genai import types

                client = genai.Client(api_key=api_key)
                prompt = self._build_analysis_prompt(lyrics)

                response = client.models.generate_content(
                    model=model_config.get('model_name', 'gemini-2.0-flash'),
                    config=types.GenerateContentConfig(
                        system_instruction=model_config.get('system_prompt', ''),
                        temperature=model_config.get('temperature', 0.7),
                        max_output_tokens=model_config.get('max_tokens', 2048)
                    ),
                    contents=prompt
                )

                content = response.text
                return self._parse_analysis_result(content)

            except ImportError:
                self.logger.warning("Google GenAI SDK未安装，使用REST API")
                return self._analyze_with_gemini_rest(lyrics)

        except Exception as e:
            self.logger.error(f"Gemini分析失败: {e}")
            return []

    def _analyze_with_gemini_rest(self, lyrics: str) -> List[Dict[str, Any]]:
        """使用Gemini REST API分析歌词"""
        try:
            model_config = config.text_models.get('gemini', {})
            api_key = model_config.get('api_key', '')
            prompt = self._build_analysis_prompt(lyrics)

            headers = {
                'Content-Type': 'application/json',
            }

            data = {
                'contents': [{
                    'parts': [{'text': prompt}]
                }],
                'generationConfig': {
                    'temperature': model_config.get('temperature', 0.7),
                    'maxOutputTokens': model_config.get('max_tokens', 2048)
                }
            }

            model_name = model_config.get('model_name', 'gemini-2.0-flash')
            base_url = model_config.get('base_url', 'https://generativelanguage.googleapis.com/v1beta/models')
            url = f"{base_url}/{model_name}:generateContent?key={api_key}"

            response = requests.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()

            result = response.json()
            content = result['candidates'][0]['content']['parts'][0]['text']

            return self._parse_analysis_result(content)

        except Exception as e:
            self.logger.error(f"Gemini REST API分析失败: {e}")
            return []
    
    def _analyze_with_zhipu(self, lyrics: str) -> List[Dict[str, Any]]:
        """使用智谱AI分析歌词"""
        try:
            model_config = config.text_models.get('zhipu', {})
            api_key = model_config.get('api_key', '')
            if not api_key:
                self.logger.error("智谱AI API密钥未配置")
                return []

            prompt = self._build_analysis_prompt(lyrics)

            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json',
            }

            data = {
                'model': model_config.get('model_name', 'glm-4.5'),
                'messages': [
                    {
                        'role': 'system',
                        'content': model_config.get('system_prompt', '')
                    },
                    {
                        'role': 'user',
                        'content': prompt
                    }
                ],
                'temperature': model_config.get('temperature', 0.7),
                'max_tokens': model_config.get('max_tokens', 2048)
            }

            url = model_config.get('base_url', 'https://open.bigmodel.cn/api/paas/v4/chat/completions')

            response = requests.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()

            result = response.json()
            content = result['choices'][0]['message']['content']

            return self._parse_analysis_result(content)

        except Exception as e:
            self.logger.error(f"智谱AI分析失败: {e}")
            return []
    
    def _analyze_with_deepseek(self, lyrics: str) -> List[Dict[str, Any]]:
        """使用DeepSeek分析歌词"""
        try:
            api_key = config.ai_config.get('deepseek_api_key', '')
            if not api_key:
                self.logger.error("DeepSeek API密钥未配置")
                return []
            
            prompt = self._build_analysis_prompt(lyrics)
            
            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json',
            }
            
            data = {
                'model': 'deepseek-chat',
                'messages': [
                    {'role': 'user', 'content': prompt}
                ],
                'temperature': 0.7,
                'max_tokens': 2048
            }
            
            url = "https://api.deepseek.com/chat/completions"
            
            response = requests.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            content = result['choices'][0]['message']['content']
            
            return self._parse_analysis_result(content)
            
        except Exception as e:
            self.logger.error(f"DeepSeek分析失败: {e}")
            return []
    
    def _build_analysis_prompt(self, lyrics: str) -> str:
        """构建分析提示词"""
        return f"""
请分析以下歌词，为每一句歌词生成对应的画面描述，用于AI绘图。

歌词：
{lyrics}

请按照以下JSON格式返回结果：
{{
    "scenes": [
        {{
            "lyric": "歌词内容",
            "description": "详细的画面描述，包含场景、人物、情感、色彩、构图等元素",
            "style": "艺术风格，如写实、动漫、油画等",
            "mood": "情感氛围，如温暖、忧郁、激昂等",
            "duration": 3.0
        }}
    ]
}}

要求：
1. 每句歌词对应一个场景
2. 描述要具体生动，适合AI绘图
3. 考虑歌词的情感和意境
4. 保持整体风格的一致性
5. duration为该场景的持续时间（秒）
"""
    
    def _parse_analysis_result(self, content: str) -> List[Dict[str, Any]]:
        """解析分析结果"""
        try:
            # 提取JSON部分
            start = content.find('{')
            end = content.rfind('}') + 1
            
            if start == -1 or end == 0:
                self.logger.error("未找到有效的JSON格式")
                return []
            
            json_str = content[start:end]
            result = json.loads(json_str)
            
            scenes = result.get('scenes', [])
            self.logger.info(f"解析到{len(scenes)}个场景")
            
            return scenes
            
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON解析失败: {e}")
            return []
        except Exception as e:
            self.logger.error(f"结果解析失败: {e}")
            return []


class ImageGenerator:
    """AI图片生成器"""
    
    def __init__(self):
        self.logger = get_logger("ImageGenerator")
        self.supported_models = {
            'gemini': self._generate_with_gemini,
            'zhipu': self._generate_with_zhipu,
            'siliconflow': self._generate_with_siliconflow,
            'doubao': self._generate_with_doubao
        }
    
    def generate_image(self, description: str, model: str = 'siliconflow', 
                      style: str = '', size: str = '1024x1024') -> Optional[str]:
        """
        生成图片
        
        Args:
            description: 图片描述
            model: AI模型名称
            style: 艺术风格
            size: 图片尺寸
            
        Returns:
            str: 生成的图片路径
        """
        try:
            if model not in self.supported_models:
                self.logger.error(f"不支持的图片生成模型: {model}")
                return None
            
            self.logger.info(f"使用{model}模型生成图片...")
            return self.supported_models[model](description, style, size)
            
        except Exception as e:
            self.logger.error(f"图片生成失败: {e}")
            return None
    
    def _generate_with_siliconflow(self, description: str, style: str, size: str) -> Optional[str]:
        """使用SiliconFlow生成图片"""
        try:
            model_config = config.image_models.get('siliconflow', {})
            api_key = model_config.get('api_key', '')
            if not api_key:
                self.logger.error("SiliconFlow API密钥未配置")
                return None

            # 构建完整的提示词
            full_prompt = f"{description}"
            if style:
                style_text = self._map_chinese_style_to_english(style)
                full_prompt += f", {style_text}"

            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json',
            }

            data = {
                'model': model_config.get('model_name', 'Kwai-Kolors/Kolors'),
                'prompt': full_prompt,
                'image_size': model_config.get('image_size', size),
                'batch_size': model_config.get('batch_size', 1),
                'num_inference_steps': model_config.get('num_inference_steps', 20),
                'guidance_scale': model_config.get('guidance_scale', 7.5)
            }

            url = model_config.get('base_url', 'https://api.siliconflow.cn/v1/images/generations')

            response = requests.post(url, headers=headers, json=data, timeout=60)
            response.raise_for_status()

            result = response.json()
            image_url = result['data'][0]['url']

            # 下载图片
            return self._download_image(image_url)

        except Exception as e:
            self.logger.error(f"SiliconFlow图片生成失败: {e}")
            return None

    def _map_chinese_style_to_english(self, chinese_style: str) -> str:
        """将中文风格映射为英文描述"""
        style_mapping = {
            '写实风格': 'photorealistic, high quality, detailed, realistic',
            '动漫风格': 'anime style, manga style, cartoon, 2D animation',
            '油画风格': 'oil painting style, artistic, classical painting, brush strokes',
            '水彩风格': 'watercolor painting style, soft colors, artistic, flowing',
            '素描风格': 'pencil sketch, hand drawn, artistic, black and white',
            '中国画风格': 'Chinese traditional painting, ink wash, oriental art, classical Chinese style',
            '赛博朋克风格': 'cyberpunk style, neon lights, futuristic, sci-fi, dark atmosphere',
            '梦幻风格': 'dreamy style, fantasy, magical, ethereal, surreal',
            '复古风格': 'vintage style, retro, nostalgic, classic, old-fashioned'
        }

        return style_mapping.get(chinese_style, chinese_style)
    
    def _generate_with_gemini(self, description: str, style: str, size: str) -> Optional[str]:
        """使用Gemini生成图片"""
        try:
            model_config = config.image_models.get('gemini', {})
            api_key = model_config.get('api_key', '')
            if not api_key:
                self.logger.error("Gemini API密钥未配置")
                return None

            # 构建完整的提示词
            full_prompt = f"{description}"
            if style:
                style_text = self._map_chinese_style_to_english(style)
                full_prompt += f", {style_text}"

            try:
                from google import genai
                from google.genai import types
                from PIL import Image
                from io import BytesIO

                client = genai.Client(api_key=api_key)

                response = client.models.generate_content(
                    model=model_config.get('model_name', 'gemini-2.0-flash-preview-image-generation'),
                    contents=full_prompt,
                    config=types.GenerateContentConfig(
                        response_modalities=model_config.get('response_modalities', ['IMAGE'])
                    )
                )

                # 保存生成的图片
                for part in response.candidates[0].content.parts:
                    if part.inline_data is not None:
                        image = Image.open(BytesIO(part.inline_data.data))

                        # 保存到本地
                        import uuid
                        images_dir = Path("./temp/images")
                        images_dir.mkdir(parents=True, exist_ok=True)
                        filename = f"gemini_image_{uuid.uuid4().hex[:8]}.png"
                        file_path = images_dir / filename

                        image.save(file_path)
                        self.logger.info(f"Gemini图片生成成功: {file_path}")
                        return str(file_path)

                return None

            except ImportError:
                self.logger.error("Google GenAI SDK未安装，无法使用Gemini图片生成")
                return None

        except Exception as e:
            self.logger.error(f"Gemini图片生成失败: {e}")
            return None

    def _generate_with_zhipu(self, description: str, style: str, size: str) -> Optional[str]:
        """使用智谱AI生成图片"""
        try:
            model_config = config.image_models.get('zhipu', {})
            api_key = model_config.get('api_key', '')
            if not api_key:
                self.logger.error("智谱AI API密钥未配置")
                return None

            # 构建完整的提示词
            full_prompt = f"{description}"
            if style:
                # 智谱AI支持中文提示词，直接使用中文风格
                full_prompt += f"，{style}"

            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json',
            }

            data = {
                'model': model_config.get('model_name', 'cogview-4-250304'),
                'prompt': full_prompt
            }

            url = model_config.get('base_url', 'https://open.bigmodel.cn/api/paas/v4/images/generations')

            response = requests.post(url, headers=headers, json=data, timeout=60)
            response.raise_for_status()

            result = response.json()
            image_url = result['data'][0]['url']

            # 下载图片
            return self._download_image(image_url)

        except Exception as e:
            self.logger.error(f"智谱AI图片生成失败: {e}")
            return None

    def _generate_with_doubao(self, description: str, style: str, size: str) -> Optional[str]:
        """使用豆包生成图片"""
        try:
            api_key = config.ai_config.get('doubao_api_key', '')
            if not api_key:
                self.logger.error("豆包API密钥未配置")
                return None

            # 豆包图片生成API实现
            # 这里需要根据豆包的实际API文档实现
            self.logger.warning("豆包图片生成功能待实现")
            return None

        except Exception as e:
            self.logger.error(f"豆包图片生成失败: {e}")
            return None
    
    def _download_image(self, image_url: str) -> Optional[str]:
        """下载图片到本地"""
        try:
            import uuid
            
            # 创建图片目录
            images_dir = Path("./temp/images")
            images_dir.mkdir(parents=True, exist_ok=True)
            
            # 生成唯一文件名
            filename = f"ai_image_{uuid.uuid4().hex[:8]}.png"
            file_path = images_dir / filename
            
            # 下载图片
            response = requests.get(image_url, timeout=30)
            response.raise_for_status()
            
            with open(file_path, 'wb') as f:
                f.write(response.content)
            
            self.logger.info(f"图片下载成功: {file_path}")
            return str(file_path)
            
        except Exception as e:
            self.logger.error(f"图片下载失败: {e}")
            return None


class AIVideoGenerator:
    """AI视频生成器 - 整合歌词分析和图片生成"""
    
    def __init__(self):
        self.logger = get_logger("AIVideoGenerator")
        self.analyzer = LyricAnalyzer()
        self.generator = ImageGenerator()
    
    def generate_video_from_lyrics(self, lyrics: str, output_path: str,
                                 analysis_model: str = 'gemini',
                                 image_model: str = 'siliconflow') -> bool:
        """
        从歌词生成AI视频

        Args:
            lyrics: 歌词内容
            output_path: 输出视频路径
            analysis_model: 歌词分析模型
            image_model: 图片生成模型

        Returns:
            bool: 生成是否成功
        """
        try:
            # 检查API密钥配置
            if not self._check_api_keys(analysis_model, image_model):
                self.logger.warning("API密钥未配置，使用演示模式")
                return self._generate_demo_video(lyrics, output_path)

            # 1. 分析歌词
            scenes = self.analyzer.analyze_lyrics(lyrics, analysis_model)
            if not scenes:
                self.logger.warning("歌词分析失败，使用演示模式")
                return self._generate_demo_video(lyrics, output_path)

            # 2. 生成图片
            image_paths = []
            for i, scene in enumerate(scenes):
                self.logger.info(f"生成第{i+1}/{len(scenes)}张图片...")

                image_path = self.generator.generate_image(
                    scene['description'],
                    image_model,
                    scene.get('style', ''),
                    '1024x1024'
                )

                if image_path:
                    image_paths.append({
                        'path': image_path,
                        'duration': scene.get('duration', 3.0),
                        'lyric': scene.get('lyric', '')
                    })
                else:
                    self.logger.warning(f"第{i+1}张图片生成失败")

            if not image_paths:
                self.logger.warning("没有成功生成任何图片，使用演示模式")
                return self._generate_demo_video(lyrics, output_path)

            # 3. 合成视频
            return self._create_video_from_images(image_paths, output_path)

        except Exception as e:
            self.logger.error(f"AI视频生成失败: {e}")
            self.logger.info("尝试使用演示模式")
            return self._generate_demo_video(lyrics, output_path)

    def _check_api_keys(self, analysis_model: str, image_model: str) -> bool:
        """检查API密钥是否配置"""
        try:
            text_config = config.text_models.get(analysis_model, {})
            image_config = config.image_models.get(image_model, {})

            text_api_key = text_config.get('api_key', '').strip()
            image_api_key = image_config.get('api_key', '').strip()

            return bool(text_api_key and image_api_key)

        except Exception:
            return False

    def _generate_demo_video(self, lyrics: str, output_path: str) -> bool:
        """生成演示视频（使用本地图片或纯色背景）"""
        try:
            self.logger.info("使用演示模式生成视频...")

            # 分析歌词行数
            lyric_lines = [line.strip() for line in lyrics.split('\n') if line.strip()]
            if not lyric_lines:
                lyric_lines = ["AI音乐视频", "演示模式"]

            # 创建演示场景
            demo_scenes = []
            for i, line in enumerate(lyric_lines):
                demo_scenes.append({
                    'lyric': line,
                    'duration': 3.0,
                    'color': self._get_demo_color(i)
                })

            # 生成演示图片
            image_paths = []
            for i, scene in enumerate(demo_scenes):
                image_path = self._create_demo_image(scene, i)
                if image_path:
                    image_paths.append({
                        'path': image_path,
                        'duration': scene['duration'],
                        'lyric': scene['lyric']
                    })

            if not image_paths:
                self.logger.error("演示图片生成失败")
                return False

            # 合成视频
            return self._create_video_from_images(image_paths, output_path)

        except Exception as e:
            self.logger.error(f"演示视频生成失败: {e}")
            return False

    def _get_demo_color(self, index: int) -> tuple:
        """获取演示颜色"""
        colors = [
            (135, 206, 235),  # 天蓝色
            (255, 182, 193),  # 浅粉色
            (144, 238, 144),  # 浅绿色
            (255, 218, 185),  # 桃色
            (221, 160, 221),  # 梅花色
            (176, 196, 222),  # 浅钢蓝色
        ]
        return colors[index % len(colors)]

    def _create_demo_image(self, scene: dict, index: int) -> Optional[str]:
        """创建演示图片"""
        try:
            from PIL import Image, ImageDraw, ImageFont
            import uuid

            # 创建图片目录
            images_dir = Path("./temp/images")
            images_dir.mkdir(parents=True, exist_ok=True)

            # 创建图片
            width, height = 1920, 1080
            color = scene['color']

            # 创建渐变背景
            image = Image.new('RGB', (width, height), color)
            draw = ImageDraw.Draw(image)

            # 添加渐变效果
            for y in range(height):
                alpha = y / height
                new_color = tuple(int(c * (0.7 + 0.3 * alpha)) for c in color)
                draw.line([(0, y), (width, y)], fill=new_color)

            # 添加文字
            try:
                # 尝试使用系统字体
                font_size = 80
                font = ImageFont.truetype("arial.ttf", font_size)
            except:
                try:
                    # 备用字体
                    font = ImageFont.truetype("C:/Windows/Fonts/simhei.ttf", font_size)
                except:
                    # 默认字体
                    font = ImageFont.load_default()

            # 计算文字位置
            text = scene['lyric']
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]

            x = (width - text_width) // 2
            y = (height - text_height) // 2

            # 绘制文字阴影
            shadow_offset = 3
            draw.text((x + shadow_offset, y + shadow_offset), text, font=font, fill=(0, 0, 0, 128))

            # 绘制文字
            draw.text((x, y), text, font=font, fill=(255, 255, 255))

            # 保存图片
            filename = f"demo_image_{index}_{uuid.uuid4().hex[:8]}.png"
            file_path = images_dir / filename
            image.save(file_path)

            self.logger.info(f"演示图片生成成功: {file_path}")
            return str(file_path)

        except Exception as e:
            self.logger.error(f"演示图片生成失败: {e}")
            return None
    
    def _create_video_from_images(self, image_data: List[Dict], output_path: str) -> bool:
        """从图片创建视频"""
        try:
            import ffmpeg
            from src.video.video_manager import video_manager
            
            # 创建临时视频片段
            temp_videos = []
            temp_dir = Path("./temp/video_segments")
            temp_dir.mkdir(parents=True, exist_ok=True)
            
            for i, data in enumerate(image_data):
                segment_path = temp_dir / f"segment_{i:03d}.mp4"
                
                # 使用FFmpeg将图片转换为视频片段
                stream = (
                    ffmpeg
                    .input(data['path'], loop=1, t=data['duration'])
                    .filter('scale', 1920, 1080)
                    .output(str(segment_path),
                           vcodec='libx264',
                           pix_fmt='yuv420p',
                           r=30)
                    .overwrite_output()
                )
                
                if video_manager.processor._run_ffmpeg_command(stream, f"图片转视频{i+1}"):
                    temp_videos.append(str(segment_path))
                else:
                    self.logger.warning(f"图片{i+1}转视频失败")
            
            if not temp_videos:
                self.logger.error("没有成功创建任何视频片段")
                return False
            
            # 拼接视频片段
            success = video_manager.processor.concatenate_videos(temp_videos, output_path)
            
            # 清理临时文件
            import shutil
            shutil.rmtree(temp_dir, ignore_errors=True)
            
            return success
            
        except Exception as e:
            self.logger.error(f"视频合成失败: {e}")
            return False


# 全局实例
ai_video_generator = AIVideoGenerator()
