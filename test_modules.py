#!/usr/bin/env python3
"""
模块测试脚本
测试各个模块的基本功能是否正常
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_config():
    """测试配置模块"""
    print("测试配置模块...")
    try:
        from src.config.settings import config
        print(f"✓ 配置模块加载成功")
        print(f"  - 临时目录: {config.get_temp_dir()}")
        print(f"  - 输出目录: {config.get_output_dir()}")
        print(f"  - FFmpeg路径: {config.get_ffmpeg_path()}")
        return True
    except Exception as e:
        print(f"✗ 配置模块测试失败: {e}")
        return False

def test_logger():
    """测试日志模块"""
    print("\n测试日志模块...")
    try:
        from src.logger.logger import get_logger
        logger = get_logger("test")
        logger.info("日志模块测试")
        print("✓ 日志模块加载成功")
        return True
    except Exception as e:
        print(f"✗ 日志模块测试失败: {e}")
        return False

def test_theme():
    """测试主题模块"""
    print("\n测试主题模块...")
    try:
        from src.theme.theme_manager import theme_manager
        themes = theme_manager.get_available_themes()
        print(f"✓ 主题模块加载成功")
        print(f"  - 可用主题: {themes}")
        print(f"  - 当前主题: {theme_manager.get_current_theme()}")
        return True
    except Exception as e:
        print(f"✗ 主题模块测试失败: {e}")
        return False

def test_audio():
    """测试音频模块"""
    print("\n测试音频模块...")
    try:
        from src.audio.audio_manager import audio_manager
        print("✓ 音频模块加载成功")
        print(f"  - 可用下载器: {list(audio_manager.downloaders.keys())}")
        
        # 测试搜索功能
        results = audio_manager.search_audio("测试", "测试", "netease")
        print(f"  - 网易云搜索结果: {len(results)}首")
        return True
    except Exception as e:
        print(f"✗ 音频模块测试失败: {e}")
        return False

def test_subtitle():
    """测试字幕模块"""
    print("\n测试字幕模块...")
    try:
        from src.subtitle.subtitle_manager import subtitle_manager
        print("✓ 字幕模块加载成功")
        print(f"  - 可用歌词提供者: {list(subtitle_manager.providers.keys())}")
        return True
    except Exception as e:
        print(f"✗ 字幕模块测试失败: {e}")
        return False

def test_video():
    """测试视频模块"""
    print("\n测试视频模块...")
    try:
        from src.video.video_manager import video_manager
        print("✓ 视频模块加载成功")
        print(f"  - 可用下载器: {list(video_manager.downloaders.keys())}")
        return True
    except Exception as e:
        print(f"✗ 视频模块测试失败: {e}")
        return False

def test_controller():
    """测试控制器模块"""
    print("\n测试控制器模块...")
    try:
        from src.core.controller import controller
        print("✓ 控制器模块加载成功")
        print(f"  - 是否运行中: {controller.is_running()}")
        return True
    except Exception as e:
        print(f"✗ 控制器模块测试失败: {e}")
        return False

def test_ui():
    """测试界面模块"""
    print("\n测试界面模块...")
    try:
        from PySide6.QtWidgets import QApplication
        from src.ui.main_window import MainWindow
        
        # 创建应用程序实例（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # 创建主窗口（但不显示）
        window = MainWindow()
        print("✓ 界面模块加载成功")
        return True
    except Exception as e:
        print(f"✗ 界面模块测试失败: {e}")
        return False

def main():
    """主函数"""
    print("AI音乐视频生成器 - 模块测试")
    print("=" * 50)
    
    tests = [
        test_config,
        test_logger,
        test_theme,
        test_audio,
        test_subtitle,
        test_video,
        test_controller,
        test_ui
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有模块测试通过！")
        return 0
    else:
        print("✗ 部分模块测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
