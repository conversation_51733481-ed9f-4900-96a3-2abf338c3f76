Metadata-Version: 2.4
Name: pysrt
Version: 1.1.2
Summary: SubRip (.srt) subtitle parser and writer
Home-page: https://github.com/byroot/pysrt
Author: <PERSON>
Author-email: <EMAIL>
License: GPLv3
Keywords: SubRip srt subtitle
Platform: Independent
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: GNU General Public License (GPL)
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.4
Classifier: Programming Language :: Python :: 3.5
Classifier: Topic :: Multimedia :: Video
Classifier: Topic :: Software Development :: Libraries
Classifier: Topic :: Text Processing :: Markup
Requires-Dist: chardet
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: platform
Dynamic: requires-dist
Dynamic: summary

pysrt
=============

pysrt is a Python library used to edit or create SubRip files.

.. image:: https://secure.travis-ci.org/byroot/pysrt.png?branch=master
  :target: http://travis-ci.org/byroot/pysrt
.. image:: https://coveralls.io/repos/byroot/pysrt/badge.png?branch=master
  :target: https://coveralls.io/r/byroot/pysrt?branch=master
.. image:: https://img.shields.io/pypi/v/pysrt.svg
  :target: https://crate.io/packages/pysrt/

Foreword
====================

pysrt is mainly designed as a library, but if you are experiencing troubles
with bad subtitles you can first try to use `ruby-osdb <https://github.com/byroot/ruby-osdb>`_
which will try to find the best subtitle for your movie. If you are still unlucky
pysrt also provide an ``srt`` command useful for either shift, split, or rescale a
*.srt* file.

Command Usage
=====================

Shifting: ::
  
    $ srt -i shift 2s500ms movie.srt

Spliting: ::

    $ srt split 58m26s movie.srt

Rescaling: ::

    $ srt -i rate 23.9 25 movie.srt

Installation
=================

pysrt is available on pypi. To intall it you can use either

pip: ::
    
    $ sudo pip install pysrt
    
or distutils: ::

    $ sudo easy_install pysrt


It is compatible with python >= 2.6 and 3.


Library Usage
=============

Import: ::

    >>> import pysrt
    
Parsing: ::

    >>> subs = pysrt.open('some/file.srt')
    # If you get a UnicodeDecodeError try to specify the encoding
    >>> subs = pysrt.open('some/file.srt', encoding='iso-8859-1')
    
SubRipFile are list-like objects of SubRipItem instances: ::
    
    >>> len(subs)
    >>> first_sub = subs[0]
    
SubRipItem instances are editable just like pure Python objects: ::
    
    >>> first_sub.text = "Hello World !"
    >>> first_sub.start.seconds = 20
    >>> first_sub.end.minutes = 5
    
Shifting: ::

    >>> subs.shift(seconds=-2) # Move all subs 2 seconds earlier
    >>> subs.shift(minutes=1)  # Move all subs 1 minutes later
    >>> subs.shift(ratio=25/23.9) # convert a 23.9 fps subtitle in 25 fps
    >>> first_sub.shift(seconds=1) # Move the first sub 1 second later
    >>> first_sub.start += {'seconds': -1} # Make the first sub start 1 second earlier
    
Removing: ::
    
    >>> del subs[12]
    
Slicing: ::
    
    >>> part = subs.slice(starts_after={'minutes': 2, 'seconds': 30}, ends_before={'minutes': 3, 'seconds': 40})
    >>> part.shift(seconds=-2)
    
Saving changes: ::
    
    >>> subs.save('other/path.srt', encoding='utf-8')
