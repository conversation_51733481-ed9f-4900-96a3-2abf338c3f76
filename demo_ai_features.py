#!/usr/bin/env python3
"""
AI功能演示脚本
展示新增的AI功能特性
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def demo_style_mapping():
    """演示风格映射功能"""
    print("🎨 风格映射功能演示")
    print("-" * 40)
    
    from src.ai.ai_manager import ImageGenerator
    
    generator = ImageGenerator()
    
    # 中文风格映射演示
    styles = [
        ("写实风格", "适合风景、人物写真"),
        ("动漫风格", "适合卡通、二次元内容"),
        ("油画风格", "适合艺术、经典场景"),
        ("水彩风格", "适合柔美、抒情场景"),
        ("素描风格", "适合简约、文艺场景"),
        ("中国画风格", "适合传统、古典场景"),
        ("赛博朋克风格", "适合科幻、未来场景"),
        ("梦幻风格", "适合奇幻、超现实场景"),
        ("复古风格", "适合怀旧、经典场景")
    ]
    
    for style, description in styles:
        english_style = generator._map_chinese_style_to_english(style)
        print(f"📌 {style}")
        print(f"   描述: {description}")
        print(f"   英文: {english_style}")
        print()

def demo_model_configuration():
    """演示模型配置功能"""
    print("⚙️ 模型配置功能演示")
    print("-" * 40)
    
    from src.config.settings import config
    
    print("📝 文本分析模型:")
    for model_name, model_config in config.text_models.items():
        print(f"  🤖 {model_name.upper()}")
        print(f"     模型: {model_config['model_name']}")
        print(f"     令牌: {model_config['max_tokens']}")
        print(f"     温度: {model_config['temperature']}")
        print(f"     URL: {model_config['base_url']}")
        print()
    
    print("🖼️ 图像生成模型:")
    for model_name, model_config in config.image_models.items():
        print(f"  🎨 {model_name.upper()}")
        print(f"     模型: {model_config['model_name']}")
        print(f"     尺寸: {model_config['image_size']}")
        print(f"     URL: {model_config['base_url']}")
        if model_name == "siliconflow":
            print(f"     步数: {model_config.get('num_inference_steps', 'N/A')}")
            print(f"     引导: {model_config.get('guidance_scale', 'N/A')}")
        print()

def demo_lyric_analysis():
    """演示歌词分析功能"""
    print("📝 歌词分析功能演示")
    print("-" * 40)
    
    from src.ai.ai_manager import LyricAnalyzer
    
    analyzer = LyricAnalyzer()
    
    # 示例歌词
    sample_lyrics = """夜空中最亮的星
能否听清
那仰望的人
心底的孤独和叹息"""
    
    print("🎵 示例歌词:")
    for line in sample_lyrics.split('\n'):
        if line.strip():
            print(f"   {line}")
    print()
    
    # 构建分析提示词
    prompt = analyzer._build_analysis_prompt(sample_lyrics)
    print("🤖 AI分析提示词:")
    print(prompt[:200] + "..." if len(prompt) > 200 else prompt)
    print()
    
    # 示例分析结果
    sample_result = '''
    {
        "scenes": [
            {
                "lyric": "夜空中最亮的星",
                "description": "深邃的夜空中，一颗明亮的星星闪烁着温暖的光芒，周围是深蓝色的天空和淡淡的云朵",
                "style": "写实风格",
                "mood": "宁静",
                "duration": 3.5
            },
            {
                "lyric": "能否听清",
                "description": "一个人的剪影仰望星空，表达倾听和期待的姿态，月光洒在大地上",
                "style": "动漫风格",
                "mood": "期待",
                "duration": 2.8
            },
            {
                "lyric": "那仰望的人",
                "description": "特写一个人仰望星空的侧脸，眼神中充满渴望和思考",
                "style": "油画风格",
                "mood": "深思",
                "duration": 3.2
            },
            {
                "lyric": "心底的孤独和叹息",
                "description": "抽象的心灵世界，蓝色调的背景中飘浮着孤独的情感符号",
                "style": "梦幻风格",
                "mood": "忧郁",
                "duration": 4.0
            }
        ]
    }
    '''
    
    scenes = analyzer._parse_analysis_result(sample_result)
    
    print("🎬 分析结果 - 分镜场景:")
    total_duration = 0
    for i, scene in enumerate(scenes, 1):
        print(f"  场景 {i}: {scene['lyric']}")
        print(f"    画面: {scene['description']}")
        print(f"    风格: {scene['style']}")
        print(f"    情感: {scene['mood']}")
        print(f"    时长: {scene['duration']}秒")
        total_duration += scene['duration']
        print()
    
    print(f"📊 总时长: {total_duration}秒")

def demo_task_configuration():
    """演示任务配置功能"""
    print("📋 任务配置功能演示")
    print("-" * 40)
    
    from src.core.controller import TaskConfig
    
    # 传统模式配置
    traditional_config = TaskConfig(
        song_title="夜空中最亮的星",
        artist="逃跑计划",
        keywords=["星空", "夜晚", "思念"],
        video_count=5,
        output_path="./output/traditional_video.mp4",
        audio_source="local",
        video_source="local",
        use_ai_generation=False
    )
    
    print("🎬 传统模式配置:")
    print(f"  歌曲: {traditional_config.song_title}")
    print(f"  歌手: {traditional_config.artist}")
    print(f"  关键词: {', '.join(traditional_config.keywords)}")
    print(f"  视频数量: {traditional_config.video_count}")
    print(f"  视频源: {traditional_config.video_source}")
    print(f"  AI生成: {traditional_config.use_ai_generation}")
    print()
    
    # AI模式配置
    ai_config = TaskConfig(
        song_title="夜空中最亮的星",
        artist="逃跑计划",
        keywords=["星空", "夜晚", "思念"],
        output_path="./output/ai_generated_video.mp4",
        audio_source="local",
        video_source="ai_generated",
        use_ai_generation=True,
        analysis_model="gemini",
        image_model="siliconflow",
        image_style="写实风格",
        custom_style=""
    )
    
    print("🤖 AI模式配置:")
    print(f"  歌曲: {ai_config.song_title}")
    print(f"  歌手: {ai_config.artist}")
    print(f"  AI生成: {ai_config.use_ai_generation}")
    print(f"  分析模型: {ai_config.analysis_model}")
    print(f"  图像模型: {ai_config.image_model}")
    print(f"  图像风格: {ai_config.image_style}")
    print(f"  视频源: {ai_config.video_source}")
    print()
    
    # 自定义风格配置
    custom_config = TaskConfig(
        song_title="赛博朋克2077",
        artist="未来音乐家",
        output_path="./output/cyberpunk_video.mp4",
        use_ai_generation=True,
        analysis_model="zhipu",
        image_model="gemini",
        image_style="自定义风格",
        custom_style="赛博朋克风格，霓虹灯，未来城市，科技感，暗色调"
    )
    
    print("🌃 自定义风格配置:")
    print(f"  歌曲: {custom_config.song_title}")
    print(f"  分析模型: {custom_config.analysis_model}")
    print(f"  图像模型: {custom_config.image_model}")
    print(f"  图像风格: {custom_config.image_style}")
    print(f"  自定义描述: {custom_config.custom_style}")

def demo_workflow():
    """演示完整工作流程"""
    print("🔄 完整工作流程演示")
    print("-" * 40)
    
    print("1️⃣ 用户输入:")
    print("   • 歌曲名称: 夜空中最亮的星")
    print("   • 歌手: 逃跑计划")
    print("   • 勾选: 使用AI生成图片")
    print("   • 分析模型: Gemini")
    print("   • 图像模型: SiliconFlow")
    print("   • 图像风格: 写实风格")
    print()
    
    print("2️⃣ AI歌词分析:")
    print("   • 获取歌词内容")
    print("   • 使用Gemini分析歌词情感和意境")
    print("   • 生成每句歌词对应的画面描述")
    print("   • 确定每个场景的时长和风格")
    print()
    
    print("3️⃣ AI图片生成:")
    print("   • 根据画面描述生成图片")
    print("   • 应用选定的艺术风格")
    print("   • 确保图片质量和一致性")
    print("   • 下载并保存生成的图片")
    print()
    
    print("4️⃣ 视频合成:")
    print("   • 将图片转换为视频片段")
    print("   • 根据歌词时长调整片段长度")
    print("   • 添加过渡效果和关键帧动画")
    print("   • 拼接所有片段形成完整视频")
    print()
    
    print("5️⃣ 后期处理:")
    print("   • 添加背景音乐")
    print("   • 添加字幕（可选）")
    print("   • 添加水印（可选）")
    print("   • 输出最终视频文件")

def main():
    """主函数"""
    print("🤖 AI音乐视频生成器 - AI功能演示")
    print("=" * 60)
    print()
    
    demos = [
        demo_style_mapping,
        demo_model_configuration,
        demo_lyric_analysis,
        demo_task_configuration,
        demo_workflow
    ]
    
    for i, demo in enumerate(demos, 1):
        try:
            demo()
            if i < len(demos):
                print("\n" + "=" * 60 + "\n")
        except Exception as e:
            print(f"演示 {i} 失败: {e}")
    
    print("=" * 60)
    print("🎉 AI功能演示完成！")
    print()
    print("📋 功能特性总结:")
    print("✅ 中文风格显示 - 9种预设风格 + 自定义风格")
    print("✅ 模型参数配置 - 完整的API和参数设置")
    print("✅ 智能歌词分析 - AI理解歌词情感和意境")
    print("✅ 分镜画面生成 - 根据歌词生成对应画面")
    print("✅ 多模型支持 - Gemini、智谱AI、DeepSeek、SiliconFlow")
    print("✅ 配置持久化 - 设置自动保存和恢复")
    print("✅ 专业界面 - 直观的设置和操作界面")
    print()
    print("🚀 现在您可以:")
    print("1. 打开主程序界面")
    print("2. 勾选'使用AI生成图片'")
    print("3. 点击'AI模型设置'配置API密钥")
    print("4. 选择喜欢的风格和模型")
    print("5. 开始生成专属的AI音乐视频！")

if __name__ == "__main__":
    main()
