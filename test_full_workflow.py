#!/usr/bin/env python3
"""
完整工作流程测试脚本
测试从音频到视频的完整生成流程
"""

import sys
from pathlib import Path
import tempfile
import os

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_full_workflow():
    """测试完整的音乐视频生成流程"""
    print("测试完整音乐视频生成流程...")
    
    try:
        from src.core.controller import MusicVideoController, TaskConfig
        from src.audio.audio_manager import audio_manager
        from src.video.video_manager import video_manager
        from src.subtitle.subtitle_manager import subtitle_manager
        
        # 创建控制器
        controller = MusicVideoController()
        
        # 创建临时输出文件
        output_dir = Path("./output")
        output_dir.mkdir(exist_ok=True)
        output_path = output_dir / "test_music_video.mp4"
        
        # 创建任务配置
        task_config = TaskConfig(
            song_title="测试歌曲",
            artist="测试歌手",
            keywords=["test", "video"],
            video_count=3,
            output_path=str(output_path),
            audio_source="local",
            video_source="local",
            lyric_provider="netease"
        )
        
        print(f"任务配置:")
        print(f"  - 歌曲: {task_config.song_title} - {task_config.artist}")
        print(f"  - 音频源: {task_config.audio_source}")
        print(f"  - 视频源: {task_config.video_source}")
        print(f"  - 输出路径: {task_config.output_path}")
        
        # 手动执行各个步骤进行测试
        
        # 1. 测试音频搜索和下载
        print("\n1. 测试音频处理...")
        audio_results = audio_manager.search_audio(
            task_config.song_title,
            task_config.artist,
            task_config.audio_source
        )
        
        if not audio_results:
            print("✗ 音频搜索失败")
            return False
        
        print(f"✓ 找到{len(audio_results)}个音频文件")
        
        # 下载音频
        audio_url = audio_results[0].get('url', '')
        audio_filename = f"{task_config.song_title}_{task_config.artist}.mp3"
        
        audio_info = audio_manager.download_audio(
            audio_url, audio_filename, task_config.audio_source
        )
        
        if not audio_info:
            print("✗ 音频下载失败")
            return False
        
        print(f"✓ 音频下载成功: {audio_info.title}")
        print(f"  - 时长: {audio_info.duration} 秒")
        print(f"  - 文件: {audio_info.file_path}")
        
        # 2. 测试歌词获取和字幕生成
        print("\n2. 测试字幕处理...")
        lyrics_content = subtitle_manager.get_lyrics(
            task_config.song_title,
            task_config.artist,
            task_config.lyric_provider
        )
        
        subtitle_path = None
        if lyrics_content:
            print("✓ 歌词获取成功")
            
            # 生成字幕文件
            subtitle_filename = f"{task_config.song_title}_{task_config.artist}.srt"
            subtitle_path = Path(tempfile.gettempdir()) / subtitle_filename
            
            success = subtitle_manager.create_subtitle(
                lyrics_content,
                audio_info.duration,
                str(subtitle_path),
                "srt"
            )
            
            if success:
                print(f"✓ 字幕文件生成成功: {subtitle_path}")
            else:
                print("⚠ 字幕文件生成失败，将生成无字幕视频")
                subtitle_path = None
        else:
            print("⚠ 未获取到歌词，将生成无字幕视频")
        
        # 3. 测试视频搜索和下载
        print("\n3. 测试视频处理...")
        video_results = video_manager.search_videos(
            task_config.keywords,
            task_config.video_count,
            task_config.video_source
        )
        
        if not video_results:
            print("✗ 视频搜索失败")
            return False
        
        print(f"✓ 找到{len(video_results)}个视频文件")
        
        # 下载视频
        video_urls = [result['url'] for result in video_results]
        video_files = video_manager.download_videos(
            video_urls, task_config.video_source
        )
        
        if not video_files:
            print("✗ 视频下载失败")
            return False
        
        print(f"✓ 视频下载成功，共{len(video_files)}个文件")
        
        # 4. 测试视频合成
        print("\n4. 测试视频合成...")
        success = video_manager.create_music_video(
            video_files,
            audio_info.file_path,
            str(subtitle_path) if subtitle_path else "",
            str(output_path),
            audio_info.duration
        )
        
        if success:
            print(f"✓ 音乐视频生成成功!")
            print(f"  - 输出文件: {output_path}")
            
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                print(f"  - 文件大小: {file_size} 字节 ({file_size/1024/1024:.1f} MB)")
                
                if file_size > 0:
                    print("🎉 完整工作流程测试成功!")
                    return True
                else:
                    print("✗ 输出文件为空")
                    return False
            else:
                print("✗ 输出文件不存在")
                return False
        else:
            print("✗ 音乐视频生成失败")
            return False
        
    except Exception as e:
        print(f"✗ 工作流程测试异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def check_prerequisites():
    """检查前置条件"""
    print("检查前置条件...")
    
    # 检查测试音频文件
    audio_dir = Path("./audio")
    if not audio_dir.exists() or not list(audio_dir.glob("*.mp3")):
        print("⚠ 未找到测试音频文件，正在创建...")
        try:
            import subprocess
            result = subprocess.run([
                sys.executable, "create_test_audio.py"
            ], capture_output=True, text=True)
            
            if result.returncode != 0:
                print("✗ 创建测试音频文件失败")
                return False
        except Exception as e:
            print(f"✗ 创建测试音频文件异常: {e}")
            return False
    
    # 检查测试视频文件
    video_dir = Path("./videos")
    if not video_dir.exists() or not list(video_dir.glob("*.mp4")):
        print("⚠ 未找到测试视频文件，正在创建...")
        try:
            import subprocess
            result = subprocess.run([
                sys.executable, "create_test_videos.py"
            ], capture_output=True, text=True)
            
            if result.returncode != 0:
                print("✗ 创建测试视频文件失败")
                return False
        except Exception as e:
            print(f"✗ 创建测试视频文件异常: {e}")
            return False
    
    print("✓ 前置条件检查通过")
    return True

def main():
    """主函数"""
    print("AI音乐视频生成器 - 完整工作流程测试")
    print("=" * 50)
    
    # 检查前置条件
    if not check_prerequisites():
        print("\n❌ 前置条件检查失败")
        return 1
    
    # 执行完整工作流程测试
    if test_full_workflow():
        print("\n🎉 完整工作流程测试成功!")
        print("\n生成的视频文件位于: ./output/test_music_video.mp4")
        print("您可以使用视频播放器查看生成的音乐视频。")
        return 0
    else:
        print("\n❌ 完整工作流程测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
