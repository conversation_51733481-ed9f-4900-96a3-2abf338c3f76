"""
主窗口模块
基于PySide6构建的图形用户界面，提供直观的操作界面和实时进度显示
包含歌曲信息输入、参数设置、进度显示和日志输出等功能
"""

import os
from pathlib import Path
from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QLineEdit, QPushButton, QTextEdit, QProgressBar,
    QGroupBox, QComboBox, QSpinBox, QFileDialog, QMessageBox,
    QSplitter, QListWidget, QCheckBox, QStatusBar, QMenuBar,
    QMenu, QApplication
)
from PySide6.QtCore import Qt, QTimer, Signal
from PySide6.QtGui import QFont, QIcon, QAction

from src.logger.logger import get_logger
from src.config.settings import config
from src.theme.theme_manager import theme_manager
from src.core.controller import controller, TaskConfig, TaskProgress


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger("MainWindow")
        self.current_task_config = None
        
        self.init_ui()
        self.connect_signals()
        self.apply_theme()
        
        self.logger.info("主窗口初始化完成")
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("AI音乐视频生成器 v1.0")
        self.setMinimumSize(1000, 700)
        self.resize(1200, 800)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧控制面板
        control_panel = self.create_control_panel()
        splitter.addWidget(control_panel)
        
        # 右侧日志面板
        log_panel = self.create_log_panel()
        splitter.addWidget(log_panel)
        
        # 设置分割器比例
        splitter.setStretchFactor(0, 2)
        splitter.setStretchFactor(1, 1)
        
        # 创建菜单栏
        self.create_menu_bar()
        
        # 创建状态栏
        self.create_status_bar()
    
    def create_control_panel(self) -> QWidget:
        """创建控制面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 歌曲信息组
        song_group = QGroupBox("歌曲信息")
        song_layout = QGridLayout(song_group)
        
        # 歌曲标题
        song_layout.addWidget(QLabel("歌曲标题:"), 0, 0)
        self.song_title_edit = QLineEdit()
        self.song_title_edit.setPlaceholderText("请输入歌曲标题")
        song_layout.addWidget(self.song_title_edit, 0, 1)
        
        # 艺术家
        song_layout.addWidget(QLabel("艺术家:"), 1, 0)
        self.artist_edit = QLineEdit()
        self.artist_edit.setPlaceholderText("请输入艺术家名称")
        song_layout.addWidget(self.artist_edit, 1, 1)
        
        # 关键词
        song_layout.addWidget(QLabel("关键词:"), 2, 0)
        self.keywords_edit = QLineEdit()
        self.keywords_edit.setPlaceholderText("用逗号分隔多个关键词")
        song_layout.addWidget(self.keywords_edit, 2, 1)
        
        layout.addWidget(song_group)
        
        # 参数设置组
        params_group = QGroupBox("参数设置")
        params_layout = QGridLayout(params_group)
        
        # 视频数量
        params_layout.addWidget(QLabel("视频素材数量:"), 0, 0)
        self.video_count_spin = QSpinBox()
        self.video_count_spin.setRange(1, 20)
        self.video_count_spin.setValue(5)
        params_layout.addWidget(self.video_count_spin, 0, 1)
        
        # 音频源
        params_layout.addWidget(QLabel("音频源:"), 1, 0)
        self.audio_source_combo = QComboBox()
        self.audio_source_combo.addItems(["netease", "youtube", "local"])
        params_layout.addWidget(self.audio_source_combo, 1, 1)

        # 视频源
        params_layout.addWidget(QLabel("视频源:"), 2, 0)
        self.video_source_combo = QComboBox()
        self.video_source_combo.addItems(["local", "pexels"])
        params_layout.addWidget(self.video_source_combo, 2, 1)
        
        # 输出路径
        params_layout.addWidget(QLabel("输出路径:"), 3, 0)
        output_layout = QHBoxLayout()
        self.output_path_edit = QLineEdit()
        self.output_path_edit.setPlaceholderText("选择输出文件路径")
        output_layout.addWidget(self.output_path_edit)
        
        self.browse_button = QPushButton("浏览...")
        self.browse_button.clicked.connect(self.browse_output_path)
        output_layout.addWidget(self.browse_button)
        
        params_layout.addLayout(output_layout, 3, 1)
        
        layout.addWidget(params_group)
        
        # 进度显示组
        progress_group = QGroupBox("生成进度")
        progress_layout = QVBoxLayout(progress_group)
        
        # 当前阶段
        self.stage_label = QLabel("准备中...")
        progress_layout.addWidget(self.stage_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        progress_layout.addWidget(self.progress_bar)
        
        # 状态消息
        self.status_label = QLabel("等待开始...")
        progress_layout.addWidget(self.status_label)
        
        layout.addWidget(progress_group)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.start_button = QPushButton("开始生成")
        self.start_button.clicked.connect(self.start_generation)
        button_layout.addWidget(self.start_button)
        
        self.stop_button = QPushButton("停止生成")
        self.stop_button.clicked.connect(self.stop_generation)
        self.stop_button.setEnabled(False)
        button_layout.addWidget(self.stop_button)
        
        self.clear_button = QPushButton("清理临时文件")
        self.clear_button.clicked.connect(self.cleanup_temp_files)
        button_layout.addWidget(self.clear_button)
        
        layout.addLayout(button_layout)
        
        # 添加弹性空间
        layout.addStretch()
        
        return panel
    
    def create_log_panel(self) -> QWidget:
        """创建日志面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 日志标题
        log_label = QLabel("运行日志")
        log_label.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        layout.addWidget(log_label)
        
        # 日志文本框
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        layout.addWidget(self.log_text)
        
        # 日志控制按钮
        log_button_layout = QHBoxLayout()
        
        self.clear_log_button = QPushButton("清空日志")
        self.clear_log_button.clicked.connect(self.clear_log)
        log_button_layout.addWidget(self.clear_log_button)
        
        self.save_log_button = QPushButton("保存日志")
        self.save_log_button.clicked.connect(self.save_log)
        log_button_layout.addWidget(self.save_log_button)
        
        log_button_layout.addStretch()
        layout.addLayout(log_button_layout)
        
        return panel
    
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件")
        
        # 新建任务
        new_action = QAction("新建任务", self)
        new_action.setShortcut("Ctrl+N")
        new_action.triggered.connect(self.new_task)
        file_menu.addAction(new_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction("退出", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 设置菜单
        settings_menu = menubar.addMenu("设置")

        # API设置
        api_action = QAction("API设置", self)
        api_action.triggered.connect(self.show_settings)
        settings_menu.addAction(api_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu("帮助")
        
        about_action = QAction("关于", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("就绪")
    
    def connect_signals(self):
        """连接信号"""
        # 连接控制器信号
        controller.progress_updated.connect(self.update_progress)
        controller.task_completed.connect(self.on_task_completed)
        
        # 连接主题变更信号
        theme_manager.theme_changed.connect(self.on_theme_changed)
    
    def apply_theme(self):
        """应用主题"""
        theme_manager.set_theme(config.app_config.get('theme', 'light'))
    
    def browse_output_path(self):
        """浏览输出路径"""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "选择输出文件",
            str(config.get_output_dir() / "music_video.mp4"),
            "视频文件 (*.mp4);;所有文件 (*)"
        )
        
        if file_path:
            self.output_path_edit.setText(file_path)
    
    def start_generation(self):
        """开始生成"""
        # 验证输入
        if not self.song_title_edit.text().strip():
            QMessageBox.warning(self, "警告", "请输入歌曲标题")
            return
        
        if not self.output_path_edit.text().strip():
            QMessageBox.warning(self, "警告", "请选择输出路径")
            return
        
        # 创建任务配置
        keywords = [k.strip() for k in self.keywords_edit.text().split(',') if k.strip()]
        
        task_config = TaskConfig(
            song_title=self.song_title_edit.text().strip(),
            artist=self.artist_edit.text().strip(),
            keywords=keywords,
            video_count=self.video_count_spin.value(),
            output_path=self.output_path_edit.text().strip(),
            audio_source=self.audio_source_combo.currentText(),
            video_source=self.video_source_combo.currentText(),
            lyric_provider="netease"
        )
        
        # 启动任务
        if controller.start_generation(task_config):
            self.current_task_config = task_config
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.status_bar.showMessage("生成中...")
            self.add_log("开始生成音乐视频...")
        else:
            QMessageBox.critical(self, "错误", "启动任务失败")
    
    def stop_generation(self):
        """停止生成"""
        controller.stop_generation()
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.status_bar.showMessage("已停止")
        self.add_log("用户停止了生成任务")
    
    def cleanup_temp_files(self):
        """清理临时文件"""
        controller.cleanup_temp_files()
        self.add_log("临时文件清理完成")
        QMessageBox.information(self, "信息", "临时文件清理完成")
    
    def update_progress(self, progress: TaskProgress):
        """更新进度"""
        self.stage_label.setText(f"当前阶段: {progress.stage}")
        self.progress_bar.setValue(int(progress.progress))
        self.status_label.setText(progress.message)
        
        if progress.error:
            self.add_log(f"错误: {progress.error}", "error")
        elif progress.message:
            self.add_log(progress.message)
    
    def on_task_completed(self, success: bool, result: str):
        """任务完成回调"""
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        
        if success:
            self.status_bar.showMessage("生成完成")
            self.add_log(f"视频生成成功: {result}", "success")
            QMessageBox.information(self, "成功", f"视频生成成功！\n输出文件: {result}")
        else:
            self.status_bar.showMessage("生成失败")
            self.add_log(f"视频生成失败: {result}", "error")
            QMessageBox.critical(self, "失败", f"视频生成失败！\n错误信息: {result}")
        
        # 重置进度
        self.progress_bar.setValue(0)
        self.stage_label.setText("准备中...")
        self.status_label.setText("等待开始...")
    
    def add_log(self, message: str, level: str = "info"):
        """添加日志"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        
        color_map = {
            "info": "black",
            "success": "green",
            "warning": "orange",
            "error": "red"
        }
        
        color = color_map.get(level, "black")
        formatted_message = f'<span style="color: {color};">[{timestamp}] {message}</span>'
        
        self.log_text.append(formatted_message)
        
        # 自动滚动到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.log_text.setTextCursor(cursor)
    
    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
    
    def save_log(self):
        """保存日志"""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "保存日志",
            str(config.get_output_dir() / "log.txt"),
            "文本文件 (*.txt);;所有文件 (*)"
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.toPlainText())
                QMessageBox.information(self, "成功", "日志保存成功")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存日志失败: {e}")
    
    def new_task(self):
        """新建任务"""
        # 清空输入框
        self.song_title_edit.clear()
        self.artist_edit.clear()
        self.keywords_edit.clear()
        self.output_path_edit.clear()
        
        # 重置参数
        self.video_count_spin.setValue(5)
        self.audio_source_combo.setCurrentIndex(0)
        self.video_source_combo.setCurrentIndex(0)
        
        # 重置进度
        self.progress_bar.setValue(0)
        self.stage_label.setText("准备中...")
        self.status_label.setText("等待开始...")
        
        self.add_log("创建新任务")
    
    def on_theme_changed(self, theme_name: str):
        """主题变更回调"""
        self.add_log(f"应用浅色主题")
    
    def show_settings(self):
        """显示设置对话框"""
        from src.ui.settings_dialog import SettingsDialog
        dialog = SettingsDialog(self)
        dialog.exec()

    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(
            self,
            "关于",
            "AI音乐视频生成器 v1.0\n\n"
            "基于Python 3.12和PySide6开发\n"
            "支持自动获取音频、歌词和视频素材\n"
            "一键生成音乐视频\n\n"
            "© 2024 AI Music Video Generator"
        )
    
    def closeEvent(self, event):
        """关闭事件"""
        if controller.is_running():
            reply = QMessageBox.question(
                self,
                "确认退出",
                "有任务正在运行，确定要退出吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                controller.stop_generation()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()
