# AI音乐视频生成器 - 使用说明

## 🎵 项目概述

AI音乐视频生成器是一个基于Python 3.12和PySide6开发的音乐视频一键生成软件。它能够：

- 🎵 从网易云音乐、YouTube等平台获取音频
- 📝 自动获取歌词并生成字幕
- 🎬 从Pexels等平台获取高质量视频素材
- 🔧 使用FFmpeg进行专业级视频合成
- 🎨 提供美观的图形用户界面

## 🚀 快速开始

### 1. 环境准备

确保您的系统已安装：
- Python 3.12+
- Git（可选）

### 2. 安装步骤

```bash
# 1. 进入项目目录
cd AI_Music

# 2. 创建虚拟环境
python -m venv venv

# 3. 激活虚拟环境
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# 4. 安装依赖
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 5. 运行程序
python src/main.py
```

### 3. 首次运行

程序首次运行时会：
- 在用户目录下创建 `.ai_music` 配置文件夹
- 自动检测FFmpeg（项目已包含）
- 初始化各个模块

## 📋 功能说明

### 主界面功能

1. **歌曲信息输入**
   - 歌曲标题：输入要生成视频的歌曲名称
   - 艺术家：输入歌手或艺术家名称
   - 关键词：输入相关关键词，用逗号分隔（用于视频素材搜索）

2. **参数设置**
   - 视频素材数量：选择要下载的视频片段数量（1-20个）
   - 音频源：选择音频获取来源（网易云音乐/YouTube）
   - 视频源：选择视频素材来源（Pexels）
   - 输出路径：选择生成视频的保存位置

3. **进度显示**
   - 实时显示当前处理阶段
   - 进度条显示完成百分比
   - 详细的状态消息

4. **日志输出**
   - 实时显示程序运行日志
   - 支持日志保存和清空
   - 彩色日志便于查看

### 菜单功能

1. **文件菜单**
   - 新建任务：清空当前输入，开始新任务
   - 退出：安全退出程序

2. **设置菜单**
   - API设置：配置Pexels等API密钥
   - 主题设置：切换界面主题（浅色/深色/蓝色）

3. **帮助菜单**
   - 关于：显示程序信息

## 🔧 API配置

### Pexels API（推荐）

1. 访问 [Pexels API](https://www.pexels.com/api/)
2. 注册账号并登录
3. 创建应用程序获取API密钥
4. 在程序中：设置 → API设置 → 输入Pexels API密钥

**限制：** 免费版本每月200次请求

### 其他API

程序还支持：
- 网易云音乐API（通过pyncm库）
- YouTube音频下载（通过yt-dlp）
- QQ音乐歌词获取

## 📝 使用流程

### 基本使用

1. **启动程序**
   ```bash
   python src/main.py
   ```

2. **配置API密钥**（首次使用）
   - 点击菜单：设置 → API设置
   - 输入Pexels API密钥
   - 点击保存

3. **输入歌曲信息**
   - 歌曲标题：例如 "起风了"
   - 艺术家：例如 "吴青峰"
   - 关键词：例如 "风景,自然,唯美"

4. **设置参数**
   - 视频素材数量：建议5-10个
   - 音频源：选择"netease"（网易云音乐）
   - 视频源：选择"pexels"
   - 输出路径：选择保存位置

5. **开始生成**
   - 点击"开始生成"按钮
   - 观察进度条和日志输出
   - 等待生成完成

### 高级功能

1. **批量处理**
   - 可以连续生成多个视频
   - 每次完成后点击"新建任务"

2. **自定义主题**
   - 设置 → 主题 → 选择喜欢的主题

3. **日志管理**
   - 查看详细的处理日志
   - 保存日志到文件
   - 清空日志记录

## 🛠️ 故障排除

### 常见问题

1. **FFmpeg未找到**
   - 项目已包含FFmpeg，通常不会出现此问题
   - 如果出现，检查 `src/bin/` 目录是否包含ffmpeg.exe

2. **网络连接问题**
   - 确保网络连接正常
   - 某些API可能需要科学上网

3. **API密钥问题**
   - 检查API密钥是否正确输入
   - 确认API密钥未过期
   - 检查API请求限制

4. **依赖包问题**
   - 重新安装依赖：`pip install -r requirements.txt`
   - 使用国内镜像源加速下载

5. **视频生成失败**
   - 检查输出目录权限
   - 确保磁盘空间充足
   - 查看日志了解具体错误

### 错误代码

- **配置错误**：检查配置文件和API密钥
- **网络错误**：检查网络连接和API可用性
- **文件错误**：检查文件权限和磁盘空间
- **FFmpeg错误**：检查FFmpeg路径和参数

## 📊 性能优化

### 建议设置

1. **视频素材数量**
   - 短歌曲（<3分钟）：3-5个素材
   - 中等歌曲（3-5分钟）：5-8个素材
   - 长歌曲（>5分钟）：8-15个素材

2. **关键词选择**
   - 使用与歌曲主题相关的词汇
   - 避免过于具体的词汇
   - 可以使用英文关键词获得更多结果

3. **系统资源**
   - 建议至少4GB内存
   - 确保有足够的磁盘空间（每个视频约100-500MB）
   - 关闭不必要的程序释放资源

## 🔄 更新日志

### v1.0.0（当前版本）
- ✅ 基础功能实现
- ✅ 网易云音乐集成
- ✅ Pexels视频素材集成
- ✅ 自动歌词获取和字幕生成
- ✅ FFmpeg视频合成
- ✅ 多主题界面
- ✅ 完整的配置系统
- ✅ 详细的日志记录

### 计划功能
- 🔄 更多音频源支持
- 🔄 更多视频素材源
- 🔄 AI视频增强
- 🔄 批量处理界面
- 🔄 自定义特效模板

## 📞 技术支持

如果您在使用过程中遇到问题：

1. 查看程序日志了解详细错误信息
2. 检查本使用说明的故障排除部分
3. 确认所有依赖包已正确安装
4. 验证API密钥配置是否正确

## 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。

---

**祝您使用愉快！🎉**
