["tests/test_audio.py::TestAudioInfo::test_audio_info_creation", "tests/test_audio.py::TestAudioInfo::test_audio_info_with_values", "tests/test_audio.py::TestAudioManager::test_cleanup_temp_files", "tests/test_audio.py::TestAudioManager::test_download_audio_invalid_source", "tests/test_audio.py::TestAudioManager::test_download_audio_mock", "tests/test_audio.py::TestAudioManager::test_get_audio_info_nonexistent", "tests/test_audio.py::TestAudioManager::test_init", "tests/test_audio.py::TestAudioManager::test_search_audio_invalid_source", "tests/test_audio.py::TestAudioManager::test_search_audio_mock", "tests/test_audio.py::TestMockMusicDownloader::test_download", "tests/test_audio.py::TestMockMusicDownloader::test_search", "tests/test_audio.py::TestYouTubeDownloader::test_init", "tests/test_audio.py::TestYouTubeDownloader::test_search_failure", "tests/test_audio.py::TestYouTubeDownloader::test_search_success", "tests/test_config.py::TestConfigManager::test_api_key_management", "tests/test_config.py::TestConfigManager::test_default_config", "tests/test_config.py::TestConfigManager::test_directory_creation", "tests/test_config.py::TestConfigManager::test_init", "tests/test_config.py::TestConfigManager::test_invalid_config_file", "tests/test_config.py::TestConfigManager::test_save_and_load_config", "tests/test_config.py::TestDataClasses::test_api_config", "tests/test_config.py::TestDataClasses::test_audio_config", "tests/test_config.py::TestDataClasses::test_subtitle_config", "tests/test_config.py::TestDataClasses::test_video_config", "tests/test_integration.py::TestIntegration::test_cleanup_temp_files", "tests/test_integration.py::TestIntegration::test_controller_get_current_task", "tests/test_integration.py::TestIntegration::test_controller_prevent_multiple_tasks", "tests/test_integration.py::TestIntegration::test_controller_start_generation_valid_config", "tests/test_integration.py::TestIntegration::test_controller_validation_empty_output", "tests/test_integration.py::TestIntegration::test_controller_validation_empty_title", "tests/test_integration.py::TestIntegration::test_controller_validation_invalid_output_dir", "tests/test_integration.py::TestIntegration::test_task_config_creation", "tests/test_integration.py::TestIntegration::test_task_config_default_keywords", "tests/test_integration.py::TestWorkflowIntegration::test_config_manager_integration", "tests/test_integration.py::TestWorkflowIntegration::test_managers_integration"]