#!/usr/bin/env python3
"""
测试PySide6兼容性
验证所有UI组件是否正确使用PySide6
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_pyside6_imports():
    """测试PySide6导入"""
    print("📦 测试PySide6导入...")
    
    try:
        # 测试基础PySide6组件
        from PySide6.QtWidgets import QApplication, QMainWindow, QWidget
        from PySide6.QtCore import Qt, Signal, QThread
        from PySide6.QtGui import QFont, QIcon
        
        print("  ✓ PySide6基础组件导入成功")
        
        # 测试主界面导入
        from src.ui.main_window import MainWindow
        print("  ✓ 主界面导入成功")
        
        # 测试AI设置对话框导入
        from src.ui.ai_settings_dialog import AISettingsDialog
        print("  ✓ AI设置对话框导入成功")
        
        # 测试控制器导入
        from src.core.controller import MusicVideoController, TaskConfig
        print("  ✓ 控制器导入成功")
        
        return True
        
    except ImportError as e:
        print(f"  ✗ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"  ✗ 其他错误: {e}")
        return False

def test_signal_compatibility():
    """测试信号兼容性"""
    print("\n📡 测试信号兼容性...")
    
    try:
        from PySide6.QtCore import QObject, Signal
        
        # 创建测试信号类
        class TestSignals(QObject):
            test_signal = Signal(str)
            progress_signal = Signal(int)
            completed_signal = Signal(bool, str)
        
        # 创建实例
        signals = TestSignals()
        
        # 测试信号连接
        def test_slot(message):
            print(f"    收到信号: {message}")
        
        signals.test_signal.connect(test_slot)
        signals.test_signal.emit("测试信号")
        
        print("  ✓ 信号系统工作正常")
        return True
        
    except Exception as e:
        print(f"  ✗ 信号测试失败: {e}")
        return False

def test_ui_creation():
    """测试UI创建"""
    print("\n🖥️ 测试UI创建...")
    
    try:
        from PySide6.QtWidgets import QApplication
        
        # 创建应用程序实例（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 测试主界面创建
        from src.ui.main_window import MainWindow
        main_window = MainWindow()
        
        # 验证主要组件
        assert hasattr(main_window, 'song_title_edit'), "缺少歌曲标题输入框"
        assert hasattr(main_window, 'artist_edit'), "缺少歌手输入框"
        assert hasattr(main_window, 'ai_generation_checkbox'), "缺少AI生成选项"
        assert hasattr(main_window, 'ai_settings_button'), "缺少AI设置按钮"
        
        print("  ✓ 主界面创建成功")
        
        # 测试AI设置对话框创建
        from src.ui.ai_settings_dialog import AISettingsDialog
        ai_dialog = AISettingsDialog(main_window)
        
        # 验证对话框组件
        assert hasattr(ai_dialog, 'text_model_combo'), "缺少文本模型选择"
        assert hasattr(ai_dialog, 'image_model_combo'), "缺少图像模型选择"
        
        print("  ✓ AI设置对话框创建成功")
        
        return True
        
    except Exception as e:
        print(f"  ✗ UI创建失败: {e}")
        return False

def test_controller_signals():
    """测试控制器信号"""
    print("\n🎮 测试控制器信号...")
    
    try:
        from src.core.controller import MusicVideoController, TaskConfig
        
        # 创建控制器
        controller = MusicVideoController()
        
        # 验证信号存在
        assert hasattr(controller, 'progress_updated'), "缺少进度更新信号"
        assert hasattr(controller, 'task_completed'), "缺少任务完成信号"
        
        # 测试信号连接
        def progress_handler(progress):
            print(f"    进度更新: {progress.percentage}%")
        
        def completion_handler(success, message):
            print(f"    任务完成: {success}, {message}")
        
        controller.progress_updated.connect(progress_handler)
        controller.task_completed.connect(completion_handler)
        
        print("  ✓ 控制器信号连接成功")
        
        # 测试任务配置
        task_config = TaskConfig(
            song_title="测试歌曲",
            artist="测试歌手",
            use_ai_generation=True
        )
        
        print("  ✓ 任务配置创建成功")
        
        return True
        
    except Exception as e:
        print(f"  ✗ 控制器测试失败: {e}")
        return False

def test_theme_compatibility():
    """测试主题兼容性"""
    print("\n🎨 测试主题兼容性...")
    
    try:
        from src.theme.theme_manager import theme_manager
        
        # 测试主题加载
        themes = theme_manager.get_available_themes()
        print(f"  可用主题: {themes}")
        
        # 测试主题应用
        current_theme = theme_manager.get_current_theme()
        print(f"  当前主题: {current_theme}")
        
        print("  ✓ 主题系统兼容")
        return True
        
    except Exception as e:
        print(f"  ✗ 主题测试失败: {e}")
        return False

def test_ai_functionality():
    """测试AI功能兼容性"""
    print("\n🤖 测试AI功能兼容性...")
    
    try:
        from src.ai.ai_manager import LyricAnalyzer, ImageGenerator
        from src.config.settings import config
        
        # 测试歌词分析器
        analyzer = LyricAnalyzer()
        print("  ✓ 歌词分析器创建成功")
        
        # 测试图片生成器
        generator = ImageGenerator()
        print("  ✓ 图片生成器创建成功")
        
        # 测试配置访问
        text_models = config.text_models
        image_models = config.image_models
        
        print(f"  文本模型数量: {len(text_models)}")
        print(f"  图像模型数量: {len(image_models)}")
        
        print("  ✓ AI功能兼容")
        return True
        
    except Exception as e:
        print(f"  ✗ AI功能测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 PySide6兼容性测试")
    print("=" * 50)
    
    tests = [
        test_pyside6_imports,
        test_signal_compatibility,
        test_ui_creation,
        test_controller_signals,
        test_theme_compatibility,
        test_ai_functionality
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {e}")
        print()  # 空行分隔
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 PySide6兼容性测试全部通过！")
        print("\n✅ 修复完成:")
        print("• AI设置对话框已修复为PySide6")
        print("• 测试脚本已修复为PySide6")
        print("• 所有UI组件使用PySide6")
        print("• 信号系统正常工作")
        print("• 主题系统兼容")
        print("• AI功能正常")
        
        print("\n🚀 现在可以正常运行:")
        print("python src/main.py")
        
        return 0
    else:
        print("⚠ 部分兼容性测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
