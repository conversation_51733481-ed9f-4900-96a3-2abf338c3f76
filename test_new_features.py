#!/usr/bin/env python3
"""
测试新功能：字幕样式、滚动模式、抽帧功能
"""

import sys
from pathlib import Path
import tempfile
import os

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_subtitle_styles():
    """测试字幕样式设置"""
    print("🎬 测试字幕样式设置...")
    
    try:
        from src.config.settings import config
        
        print("当前字幕配置:")
        print(f"  字体大小: {config.subtitle.font_size}")
        print(f"  字体族: {config.subtitle.font_family}")
        print(f"  字体颜色: {config.subtitle.font_color}")
        print(f"  描边颜色: {config.subtitle.outline_color}")
        print(f"  描边宽度: {config.subtitle.outline_width}")
        print(f"  位置: {config.subtitle.position}")
        print(f"  显示模式: {config.subtitle.display_mode}")
        print(f"  滚动速度: {config.subtitle.scroll_speed}")
        print(f"  滚动方向: {config.subtitle.scroll_direction}")
        
        # 测试修改字幕样式
        print("\n🔧 测试字幕样式修改:")
        original_font_size = config.subtitle.font_size
        config.subtitle.font_size = 32
        print(f"  字体大小: {original_font_size} → {config.subtitle.font_size}")
        
        original_color = config.subtitle.font_color
        config.subtitle.font_color = "#FFD700"  # 金色
        print(f"  字体颜色: {original_color} → {config.subtitle.font_color}")
        
        original_mode = config.subtitle.display_mode
        config.subtitle.display_mode = "scroll"
        print(f"  显示模式: {original_mode} → {config.subtitle.display_mode}")
        
        # 恢复原设置
        config.subtitle.font_size = original_font_size
        config.subtitle.font_color = original_color
        config.subtitle.display_mode = original_mode
        print("  (已恢复原设置)")
        
        return True
        
    except Exception as e:
        print(f"✗ 字幕样式测试失败: {e}")
        return False

def test_frame_extraction():
    """测试抽帧功能"""
    print("\n🖼️ 测试视频抽帧功能...")
    
    try:
        from src.video.video_manager import video_manager
        from src.config.settings import config
        
        # 查找测试视频
        video_dir = Path("./videos")
        video_files = list(video_dir.glob("*.mp4"))
        
        if not video_files:
            print("⚠ 未找到测试视频文件")
            return False
        
        test_video = video_files[0]
        print(f"使用测试视频: {test_video.name}")
        
        # 创建临时输出目录
        with tempfile.TemporaryDirectory() as temp_dir:
            frame_dir = Path(temp_dir) / "frames"
            
            # 测试抽帧
            extracted_frames = video_manager.processor.extract_frames(
                str(test_video),
                str(frame_dir),
                interval=3.0,  # 每3秒抽一帧
                count=5,       # 抽5帧
                quality="medium"
            )
            
            if extracted_frames:
                print(f"✓ 抽帧成功: {len(extracted_frames)}帧")
                for i, frame_path in enumerate(extracted_frames):
                    frame_file = Path(frame_path)
                    if frame_file.exists():
                        size = frame_file.stat().st_size
                        print(f"  帧{i+1}: {frame_file.name} ({size} 字节)")
                    else:
                        print(f"  帧{i+1}: 文件不存在")
                return True
            else:
                print("✗ 抽帧失败")
                return False
        
    except Exception as e:
        print(f"✗ 抽帧测试失败: {e}")
        return False

def test_subtitle_processing():
    """测试字幕处理功能"""
    print("\n📝 测试字幕处理功能...")
    
    try:
        from src.video.video_manager import video_manager
        
        # 查找测试视频和字幕
        video_dir = Path("./videos")
        video_files = list(video_dir.glob("*.mp4"))
        
        if not video_files:
            print("⚠ 未找到测试视频文件")
            return False
        
        # 创建测试字幕文件
        test_subtitle_content = """1
00:00:00,000 --> 00:00:03,000
这是第一行字幕

2
00:00:03,000 --> 00:00:06,000
这是第二行字幕

3
00:00:06,000 --> 00:00:09,000
测试字幕样式和效果
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.srt', delete=False, encoding='utf-8') as f:
            f.write(test_subtitle_content)
            subtitle_path = f.name
        
        try:
            # 测试字幕解析
            subtitle_lines = video_manager.processor._parse_subtitle_file(subtitle_path)
            
            if subtitle_lines:
                print(f"✓ 字幕解析成功: {len(subtitle_lines)}行")
                for i, (start, end, text) in enumerate(subtitle_lines):
                    print(f"  字幕{i+1}: {start:.1f}s-{end:.1f}s '{text}'")
                
                # 测试时间转换
                test_time = "00:01:30,500"
                seconds = video_manager.processor._time_to_seconds(test_time)
                print(f"  时间转换测试: {test_time} → {seconds}秒")
                
                return True
            else:
                print("✗ 字幕解析失败")
                return False
                
        finally:
            # 清理临时文件
            if os.path.exists(subtitle_path):
                os.unlink(subtitle_path)
        
    except Exception as e:
        print(f"✗ 字幕处理测试失败: {e}")
        return False

def test_advanced_config():
    """测试高级配置功能"""
    print("\n⚙️ 测试高级配置功能...")
    
    try:
        from src.config.settings import config
        
        print("当前高级配置:")
        print(f"  抽帧启用: {config.app_config.get('frame_extract_enabled')}")
        print(f"  抽帧间隔: {config.app_config.get('frame_extract_interval')}秒")
        print(f"  抽帧数量: {config.app_config.get('frame_extract_count')}帧")
        print(f"  抽帧质量: {config.app_config.get('frame_extract_quality')}")
        print(f"  并发下载: {config.app_config.get('max_concurrent_downloads')}")
        print(f"  自动清理: {config.app_config.get('auto_cleanup')}")
        
        # 测试配置修改
        print("\n🔧 测试配置修改:")
        original_enabled = config.app_config.get('frame_extract_enabled')
        config.app_config['frame_extract_enabled'] = True
        print(f"  抽帧启用: {original_enabled} → {config.app_config['frame_extract_enabled']}")
        
        original_interval = config.app_config.get('frame_extract_interval')
        config.app_config['frame_extract_interval'] = 2.0
        print(f"  抽帧间隔: {original_interval} → {config.app_config['frame_extract_interval']}")
        
        # 恢复原设置
        config.app_config['frame_extract_enabled'] = original_enabled
        config.app_config['frame_extract_interval'] = original_interval
        print("  (已恢复原设置)")
        
        return True
        
    except Exception as e:
        print(f"✗ 高级配置测试失败: {e}")
        return False

def test_settings_ui():
    """测试设置界面"""
    print("\n🖥️ 测试设置界面...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from src.ui.settings_dialog import SettingsDialog
        
        # 创建应用程序实例
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # 创建设置对话框
        dialog = SettingsDialog()
        
        print("✓ 设置对话框创建成功")
        
        # 检查新增的控件
        new_controls = [
            'subtitle_font_size_spin',
            'subtitle_font_family_combo',
            'subtitle_font_color_btn',
            'subtitle_display_mode_combo',
            'frame_extract_enabled_check',
            'frame_extract_interval_spin'
        ]
        
        for control_name in new_controls:
            if hasattr(dialog, control_name):
                print(f"  ✓ {control_name} 存在")
            else:
                print(f"  ✗ {control_name} 不存在")
        
        return True
        
    except Exception as e:
        print(f"✗ 设置界面测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🎬 AI音乐视频生成器 - 新功能测试")
    print("=" * 50)
    
    tests = [
        test_subtitle_styles,
        test_advanced_config,
        test_subtitle_processing,
        test_frame_extraction,
        test_settings_ui
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()  # 空行分隔
        except Exception as e:
            print(f"✗ 测试异常: {e}")
            print()
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有新功能测试通过！")
        print("\n📋 新功能总结:")
        print("1. ✅ 字幕样式设置 - 字体、颜色、描边、位置")
        print("2. ✅ 字幕显示模式 - 静态、滚动、卡拉OK")
        print("3. ✅ 视频抽帧功能 - 可配置间隔、数量、质量")
        print("4. ✅ 增强设置界面 - 字幕和高级设置选项卡")
        print("5. ✅ 字幕处理优化 - 多种备用方案")
        return 0
    else:
        print("⚠ 部分新功能测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
