#!/usr/bin/env python3
"""
测试水印和抽帧功能
"""

import sys
from pathlib import Path
import tempfile
import os

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_watermark_config():
    """测试水印配置"""
    print("💧 测试水印配置...")
    
    try:
        from src.config.settings import config
        
        print("当前水印配置:")
        print(f"  启用状态: {config.watermark.enabled}")
        print(f"  水印文本: {config.watermark.text}")
        print(f"  字体大小: {config.watermark.font_size}")
        print(f"  字体颜色: {config.watermark.font_color}")
        print(f"  透明度: {config.watermark.opacity}")
        print(f"  位置: {config.watermark.position}")
        print(f"  移动启用: {config.watermark.movement_enabled}")
        print(f"  移动类型: {config.watermark.movement_type}")
        print(f"  移动速度: {config.watermark.movement_speed}")
        
        # 测试配置修改
        print("\n🔧 测试水印配置修改:")
        original_enabled = config.watermark.enabled
        config.watermark.enabled = True
        print(f"  启用状态: {original_enabled} → {config.watermark.enabled}")
        
        original_text = config.watermark.text
        config.watermark.text = "测试水印"
        print(f"  水印文本: '{original_text}' → '{config.watermark.text}'")
        
        original_movement = config.watermark.movement_enabled
        config.watermark.movement_enabled = True
        print(f"  移动启用: {original_movement} → {config.watermark.movement_enabled}")
        
        # 恢复原设置
        config.watermark.enabled = original_enabled
        config.watermark.text = original_text
        config.watermark.movement_enabled = original_movement
        print("  (已恢复原设置)")
        
        return True
        
    except Exception as e:
        print(f"✗ 水印配置测试失败: {e}")
        return False

def test_watermark_processing():
    """测试水印处理功能"""
    print("\n🎨 测试水印处理功能...")
    
    try:
        from src.video.video_manager import video_manager
        from src.config.settings import config
        
        # 查找测试视频
        video_dir = Path("./videos")
        video_files = list(video_dir.glob("*.mp4"))
        
        if not video_files:
            print("⚠ 未找到测试视频文件")
            return False
        
        test_video = video_files[0]
        print(f"使用测试视频: {test_video.name}")
        
        # 启用水印进行测试
        original_enabled = config.watermark.enabled
        original_text = config.watermark.text
        
        config.watermark.enabled = True
        config.watermark.text = "测试水印"
        config.watermark.movement_enabled = True
        config.watermark.movement_type = "circular"
        
        try:
            # 创建临时输出文件
            with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as f:
                output_path = f.name
            
            # 测试水印添加
            success = video_manager.processor.add_watermark_to_video(
                str(test_video), output_path
            )
            
            if success and os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                print(f"✓ 水印添加成功")
                print(f"  输出文件: {output_path}")
                print(f"  文件大小: {file_size} 字节")
                
                # 清理临时文件
                os.unlink(output_path)
                return True
            else:
                print("✗ 水印添加失败")
                return False
                
        finally:
            # 恢复原设置
            config.watermark.enabled = original_enabled
            config.watermark.text = original_text
        
    except Exception as e:
        print(f"✗ 水印处理测试失败: {e}")
        return False

def test_improved_frame_extraction():
    """测试改进的抽帧功能"""
    print("\n🖼️ 测试改进的抽帧功能...")
    
    try:
        from src.video.video_manager import video_manager
        
        # 查找测试视频
        video_dir = Path("./videos")
        video_files = list(video_dir.glob("*.mp4"))
        
        if not video_files:
            print("⚠ 未找到测试视频文件")
            return False
        
        test_video = video_files[0]
        print(f"使用测试视频: {test_video.name}")
        
        # 创建临时输出目录
        with tempfile.TemporaryDirectory() as temp_dir:
            frame_dir = Path(temp_dir) / "frames"
            
            # 测试抽帧（使用更保守的参数）
            extracted_frames = video_manager.processor.extract_frames(
                str(test_video),
                str(frame_dir),
                interval=2.0,  # 每2秒抽一帧
                count=3,       # 抽3帧
                quality="medium"
            )
            
            if extracted_frames:
                print(f"✓ 抽帧成功: {len(extracted_frames)}帧")
                for i, frame_path in enumerate(extracted_frames):
                    frame_file = Path(frame_path)
                    if frame_file.exists():
                        size = frame_file.stat().st_size
                        print(f"  帧{i+1}: {frame_file.name} ({size} 字节)")
                    else:
                        print(f"  帧{i+1}: 文件不存在")
                return True
            else:
                print("✗ 抽帧失败")
                return False
        
    except Exception as e:
        print(f"✗ 抽帧测试失败: {e}")
        return False

def test_watermark_movement_expressions():
    """测试水印移动表达式"""
    print("\n📐 测试水印移动表达式...")
    
    try:
        from src.video.video_manager import VideoProcessor
        from src.config.settings import config
        
        processor = VideoProcessor()
        
        # 模拟视频尺寸
        width, height = 1920, 1080
        base_x, base_y = 100, 100
        
        # 测试不同移动类型
        movement_types = ["linear", "circular", "bounce", "random"]
        
        for movement_type in movement_types:
            config.watermark.movement_type = movement_type
            config.watermark.movement_speed = 1.0
            config.watermark.movement_range = 50
            
            x_expr, y_expr = processor._build_movement_expressions(
                config.watermark, base_x, base_y, width, height
            )
            
            print(f"  {movement_type} 移动:")
            print(f"    X表达式: {x_expr}")
            print(f"    Y表达式: {y_expr}")
        
        return True
        
    except Exception as e:
        print(f"✗ 移动表达式测试失败: {e}")
        return False

def test_settings_ui_watermark():
    """测试水印设置界面"""
    print("\n🖥️ 测试水印设置界面...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from src.ui.settings_dialog import SettingsDialog
        
        # 创建应用程序实例
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # 创建设置对话框
        dialog = SettingsDialog()
        
        print("✓ 设置对话框创建成功")
        
        # 检查水印相关控件
        watermark_controls = [
            'watermark_enabled_check',
            'watermark_text_edit',
            'watermark_font_size_spin',
            'watermark_position_combo',
            'watermark_movement_enabled_check',
            'watermark_movement_type_combo'
        ]
        
        for control_name in watermark_controls:
            if hasattr(dialog, control_name):
                print(f"  ✓ {control_name} 存在")
            else:
                print(f"  ✗ {control_name} 不存在")
        
        return True
        
    except Exception as e:
        print(f"✗ 水印设置界面测试失败: {e}")
        return False

def test_complete_workflow_with_watermark():
    """测试包含水印的完整工作流程"""
    print("\n🎬 测试包含水印的完整工作流程...")
    
    try:
        from src.config.settings import config
        
        # 启用水印和抽帧
        config.watermark.enabled = True
        config.watermark.text = "AI Music Video"
        config.watermark.movement_enabled = True
        config.watermark.movement_type = "linear"
        
        config.app_config['frame_extract_enabled'] = True
        config.app_config['frame_extract_interval'] = 3.0
        config.app_config['frame_extract_count'] = 2
        
        print("✓ 工作流程配置完成")
        print(f"  水印启用: {config.watermark.enabled}")
        print(f"  水印文本: {config.watermark.text}")
        print(f"  移动效果: {config.watermark.movement_enabled}")
        print(f"  抽帧启用: {config.app_config['frame_extract_enabled']}")
        print(f"  抽帧间隔: {config.app_config['frame_extract_interval']}秒")
        
        # 恢复默认设置
        config.watermark.enabled = False
        config.app_config['frame_extract_enabled'] = False
        
        return True
        
    except Exception as e:
        print(f"✗ 完整工作流程测试失败: {e}")
        return False

def main():
    """主函数"""
    print("💧 AI音乐视频生成器 - 水印和抽帧功能测试")
    print("=" * 60)
    
    tests = [
        test_watermark_config,
        test_watermark_movement_expressions,
        test_improved_frame_extraction,
        test_watermark_processing,
        test_settings_ui_watermark,
        test_complete_workflow_with_watermark
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()  # 空行分隔
        except Exception as e:
            print(f"✗ 测试异常: {e}")
            print()
    
    print("=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有水印和抽帧功能测试通过！")
        print("\n📋 新功能总结:")
        print("1. ✅ 完善抽帧方法 - 多种备用方案，更稳定")
        print("2. ✅ 移动水印功能 - 支持多种移动模式")
        print("3. ✅ 水印配置系统 - 完整的参数设置")
        print("4. ✅ 水印设置界面 - 用户友好的配置界面")
        print("5. ✅ 集成到工作流程 - 无缝集成到视频生成流程")
        
        print("\n🎯 水印移动模式:")
        print("• linear - 直线移动")
        print("• circular - 圆形移动")
        print("• bounce - 弹跳移动")
        print("• random - 随机移动")
        
        return 0
    else:
        print("⚠ 部分功能测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
