#!/usr/bin/env python3
"""
创建测试音频文件
用于测试本地音频功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def create_test_audio():
    """创建测试音频文件"""
    try:
        from src.config.settings import config
        import subprocess
        
        # 获取FFmpeg路径
        ffmpeg_path = config.get_ffmpeg_path()
        
        # 创建音频目录
        audio_dir = Path("./audio")
        audio_dir.mkdir(exist_ok=True)
        
        # 创建一个30秒的测试音频文件
        test_audio_path = audio_dir / "测试歌曲_测试歌手.mp3"
        
        print(f"创建测试音频文件: {test_audio_path}")
        
        # 使用FFmpeg生成一个简单的音频文件（440Hz正弦波，30秒）
        cmd = [
            str(ffmpeg_path),
            '-f', 'lavfi',
            '-i', 'sine=frequency=440:duration=30',
            '-c:a', 'libmp3lame',  # 明确指定MP3编码器
            '-b:a', '128k',
            '-ar', '44100',  # 采样率
            '-ac', '2',      # 双声道
            '-metadata', f'title=测试歌曲',
            '-metadata', f'artist=测试歌手',
            '-metadata', f'album=测试专辑',
            '-y',  # 覆盖已存在的文件
            str(test_audio_path)
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✓ 测试音频文件创建成功: {test_audio_path}")
            print(f"  文件大小: {test_audio_path.stat().st_size} 字节")
            return True
        else:
            print(f"✗ 创建测试音频文件失败:")
            print(f"  错误: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"✗ 创建测试音频文件异常: {e}")
        return False

def main():
    """主函数"""
    print("创建测试音频文件")
    print("=" * 30)
    
    if create_test_audio():
        print("\n🎉 测试音频文件创建成功！")
        print("\n使用方法：")
        print("1. 在程序中选择音频源为 'local'")
        print("2. 输入歌曲标题：测试歌曲")
        print("3. 输入艺术家：测试歌手")
        print("4. 开始生成视频")
        return 0
    else:
        print("\n❌ 测试音频文件创建失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
