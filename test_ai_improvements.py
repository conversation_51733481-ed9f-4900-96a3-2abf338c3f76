#!/usr/bin/env python3
"""
测试AI功能改进
包括中文风格、自定义风格、模型参数设置等
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_chinese_style_mapping():
    """测试中文风格映射"""
    print("🎨 测试中文风格映射...")
    
    try:
        from src.ai.ai_manager import ImageGenerator
        
        generator = ImageGenerator()
        
        # 测试中文风格映射
        test_styles = [
            "写实风格", "动漫风格", "油画风格", "水彩风格", "素描风格",
            "中国画风格", "赛博朋克风格", "梦幻风格", "复古风格"
        ]
        
        for style in test_styles:
            english_style = generator._map_chinese_style_to_english(style)
            print(f"  {style} -> {english_style}")
        
        # 测试自定义风格
        custom_style = "科幻风格，未来感，霓虹灯效果"
        mapped_style = generator._map_chinese_style_to_english(custom_style)
        print(f"  自定义: {custom_style} -> {mapped_style}")
        
        print("  ✓ 中文风格映射功能正常")
        return True
        
    except Exception as e:
        print(f"✗ 中文风格映射测试失败: {e}")
        return False

def test_model_configuration():
    """测试模型配置"""
    print("\n⚙️ 测试模型配置...")
    
    try:
        from src.config.settings import config
        
        # 测试文本模型配置
        text_models = config.text_models
        print(f"  文本模型数量: {len(text_models)}")
        
        for model_name, model_config in text_models.items():
            print(f"  {model_name}:")
            print(f"    模型名称: {model_config.get('model_name', 'N/A')}")
            print(f"    最大令牌: {model_config.get('max_tokens', 'N/A')}")
            print(f"    温度: {model_config.get('temperature', 'N/A')}")
            print(f"    基础URL: {model_config.get('base_url', 'N/A')}")
        
        # 测试图像模型配置
        image_models = config.image_models
        print(f"\n  图像模型数量: {len(image_models)}")
        
        for model_name, model_config in image_models.items():
            print(f"  {model_name}:")
            print(f"    模型名称: {model_config.get('model_name', 'N/A')}")
            print(f"    图片尺寸: {model_config.get('image_size', 'N/A')}")
            print(f"    基础URL: {model_config.get('base_url', 'N/A')}")
        
        print("  ✓ 模型配置结构正常")
        return True
        
    except Exception as e:
        print(f"✗ 模型配置测试失败: {e}")
        return False

def test_ai_settings_dialog():
    """测试AI设置对话框"""
    print("\n🖥️ 测试AI设置对话框...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from src.ui.ai_settings_dialog import AISettingsDialog
        
        # 创建应用程序实例（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建对话框
        dialog = AISettingsDialog()
        
        # 测试界面组件
        assert hasattr(dialog, 'text_model_combo'), "缺少文本模型选择组件"
        assert hasattr(dialog, 'image_model_combo'), "缺少图像模型选择组件"
        assert hasattr(dialog, 'text_settings'), "缺少文本模型设置组件"
        assert hasattr(dialog, 'image_settings'), "缺少图像模型设置组件"
        
        # 测试模型数量
        text_model_count = dialog.text_model_combo.count()
        image_model_count = dialog.image_model_combo.count()
        
        print(f"  文本模型选项: {text_model_count}")
        print(f"  图像模型选项: {image_model_count}")
        
        assert text_model_count >= 3, "文本模型选项不足"
        assert image_model_count >= 3, "图像模型选项不足"
        
        print("  ✓ AI设置对话框结构正常")
        return True
        
    except Exception as e:
        print(f"✗ AI设置对话框测试失败: {e}")
        return False

def test_task_config_ai_options():
    """测试任务配置AI选项"""
    print("\n📋 测试任务配置AI选项...")
    
    try:
        from src.core.controller import TaskConfig
        
        # 创建包含AI选项的任务配置
        task_config = TaskConfig(
            song_title="测试歌曲",
            artist="测试歌手",
            use_ai_generation=True,
            analysis_model="gemini",
            image_model="siliconflow",
            image_style="写实风格",
            custom_style="科幻风格，未来感"
        )
        
        # 验证AI选项
        assert task_config.use_ai_generation == True, "AI生成选项设置失败"
        assert task_config.analysis_model == "gemini", "分析模型设置失败"
        assert task_config.image_model == "siliconflow", "图像模型设置失败"
        assert task_config.image_style == "写实风格", "图像风格设置失败"
        assert task_config.custom_style == "科幻风格，未来感", "自定义风格设置失败"
        
        print(f"  AI生成: {task_config.use_ai_generation}")
        print(f"  分析模型: {task_config.analysis_model}")
        print(f"  图像模型: {task_config.image_model}")
        print(f"  图像风格: {task_config.image_style}")
        print(f"  自定义风格: {task_config.custom_style}")
        
        print("  ✓ 任务配置AI选项正常")
        return True
        
    except Exception as e:
        print(f"✗ 任务配置AI选项测试失败: {e}")
        return False

def test_lyric_analysis_structure():
    """测试歌词分析结构"""
    print("\n📝 测试歌词分析结构...")
    
    try:
        from src.ai.ai_manager import LyricAnalyzer
        
        analyzer = LyricAnalyzer()
        
        # 测试JSON解析
        test_json = '''
        {
            "scenes": [
                {
                    "lyric": "夜空中最亮的星",
                    "description": "夜空中闪烁的明亮星星，深蓝色的天空背景，写实风格",
                    "style": "写实风格",
                    "mood": "宁静",
                    "duration": 3.0
                },
                {
                    "lyric": "能否听清",
                    "description": "一个人仰望星空的剪影，表达倾听的姿态，动漫风格",
                    "style": "动漫风格",
                    "mood": "期待",
                    "duration": 2.5
                }
            ]
        }
        '''
        
        scenes = analyzer._parse_analysis_result(test_json)
        
        assert len(scenes) == 2, f"场景数量错误，期望2个，实际{len(scenes)}个"
        
        # 验证第一个场景
        scene1 = scenes[0]
        assert scene1['lyric'] == "夜空中最亮的星", "第一个场景歌词错误"
        assert scene1['style'] == "写实风格", "第一个场景风格错误"
        assert scene1['duration'] == 3.0, "第一个场景时长错误"
        
        # 验证第二个场景
        scene2 = scenes[1]
        assert scene2['lyric'] == "能否听清", "第二个场景歌词错误"
        assert scene2['style'] == "动漫风格", "第二个场景风格错误"
        assert scene2['duration'] == 2.5, "第二个场景时长错误"
        
        print(f"  解析场景数量: {len(scenes)}")
        for i, scene in enumerate(scenes):
            print(f"  场景{i+1}: {scene['lyric']} - {scene['style']} - {scene['duration']}秒")
        
        print("  ✓ 歌词分析结构正常")
        return True
        
    except Exception as e:
        print(f"✗ 歌词分析结构测试失败: {e}")
        return False

def test_configuration_persistence():
    """测试配置持久化"""
    print("\n💾 测试配置持久化...")
    
    try:
        from src.config.settings import config
        import tempfile
        import os
        
        # 备份原始配置
        original_api_key = config.text_models['gemini']['api_key']
        original_model_name = config.text_models['gemini']['model_name']
        
        # 修改配置
        test_api_key = "test_api_key_12345"
        test_model_name = "test_model_name"
        
        config.text_models['gemini']['api_key'] = test_api_key
        config.text_models['gemini']['model_name'] = test_model_name
        
        # 保存配置
        config.save_config()
        
        # 重新加载配置
        config.load_config()
        
        # 验证配置是否正确保存和加载
        loaded_api_key = config.text_models['gemini']['api_key']
        loaded_model_name = config.text_models['gemini']['model_name']
        
        assert loaded_api_key == test_api_key, f"API密钥保存失败，期望{test_api_key}，实际{loaded_api_key}"
        assert loaded_model_name == test_model_name, f"模型名称保存失败，期望{test_model_name}，实际{loaded_model_name}"
        
        # 恢复原始配置
        config.text_models['gemini']['api_key'] = original_api_key
        config.text_models['gemini']['model_name'] = original_model_name
        config.save_config()
        
        print(f"  测试API密钥: {test_api_key} ✓")
        print(f"  测试模型名称: {test_model_name} ✓")
        print("  ✓ 配置持久化功能正常")
        return True
        
    except Exception as e:
        print(f"✗ 配置持久化测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🤖 AI音乐视频生成器 - AI功能改进测试")
    print("=" * 60)
    
    tests = [
        test_chinese_style_mapping,
        test_model_configuration,
        test_ai_settings_dialog,
        test_task_config_ai_options,
        test_lyric_analysis_structure,
        test_configuration_persistence
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {e}")
        print()  # 空行分隔
    
    print("=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有AI功能改进测试通过！")
        print("\n📋 新功能总结:")
        print("1. ✅ 中文风格显示 - 支持9种预设中文风格")
        print("2. ✅ 自定义风格 - 用户可输入任意风格描述")
        print("3. ✅ 文本模型设置 - API密钥、模型名称、参数配置")
        print("4. ✅ 图像模型设置 - 完整的模型参数配置")
        print("5. ✅ 配置持久化 - 设置自动保存和加载")
        print("6. ✅ 界面优化 - 专业的设置对话框")
        
        print("\n🎨 支持的风格:")
        print("• 写实风格 - 高质量写实图片")
        print("• 动漫风格 - 日式动漫卡通风格")
        print("• 油画风格 - 经典油画艺术风格")
        print("• 水彩风格 - 柔和水彩画风格")
        print("• 素描风格 - 手绘素描风格")
        print("• 中国画风格 - 传统中国画风格")
        print("• 赛博朋克风格 - 科幻未来风格")
        print("• 梦幻风格 - 奇幻梦境风格")
        print("• 复古风格 - 怀旧复古风格")
        print("• 自定义风格 - 用户自由定义")
        
        return 0
    else:
        print("⚠ 部分AI功能改进测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
