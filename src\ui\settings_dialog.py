"""
设置对话框模块
提供API密钥配置和其他应用程序设置的界面
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QLineEdit, QPushButton, QTabWidget,
    QWidget, QGroupBox, QMessageBox, QComboBox, QSpinBox,
    QCheckBox, QDoubleSpinBox, QColorDialog
)
from PySide6.QtCore import Qt

from src.config.settings import config
from src.theme.theme_manager import theme_manager
from src.logger.logger import get_logger


class SettingsDialog(QDialog):
    """设置对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger("SettingsDialog")
        self.init_ui()
        self.load_settings()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("设置")
        self.setModal(True)
        self.resize(500, 400)
        
        layout = QVBoxLayout(self)
        
        # 创建选项卡
        tab_widget = QTabWidget()
        layout.addWidget(tab_widget)
        
        # API设置选项卡
        api_tab = self.create_api_tab()
        tab_widget.addTab(api_tab, "API设置")

        # 视频设置选项卡
        video_tab = self.create_video_tab()
        tab_widget.addTab(video_tab, "视频设置")

        # 音频设置选项卡
        audio_tab = self.create_audio_tab()
        tab_widget.addTab(audio_tab, "音频设置")

        # 字幕设置选项卡
        subtitle_tab = self.create_subtitle_tab()
        tab_widget.addTab(subtitle_tab, "字幕设置")

        # 高级设置选项卡
        advanced_tab = self.create_advanced_tab()
        tab_widget.addTab(advanced_tab, "高级设置")
        
        # 按钮
        button_layout = QHBoxLayout()
        
        self.save_button = QPushButton("保存")
        self.save_button.clicked.connect(self.save_settings)
        button_layout.addWidget(self.save_button)
        
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
    
    def create_api_tab(self) -> QWidget:
        """创建API设置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Pexels API设置
        pexels_group = QGroupBox("Pexels API")
        pexels_layout = QGridLayout(pexels_group)
        
        pexels_layout.addWidget(QLabel("API密钥:"), 0, 0)
        self.pexels_api_edit = QLineEdit()
        self.pexels_api_edit.setPlaceholderText("请输入Pexels API密钥")
        self.pexels_api_edit.setEchoMode(QLineEdit.Password)
        pexels_layout.addWidget(self.pexels_api_edit, 0, 1)
        
        pexels_help_label = QLabel(
            '<a href="https://www.pexels.com/api/">获取Pexels API密钥</a>'
        )
        pexels_help_label.setOpenExternalLinks(True)
        pexels_layout.addWidget(pexels_help_label, 1, 1)
        
        layout.addWidget(pexels_group)
        
        # Pixabay API设置
        pixabay_group = QGroupBox("Pixabay API")
        pixabay_layout = QGridLayout(pixabay_group)
        
        pixabay_layout.addWidget(QLabel("API密钥:"), 0, 0)
        self.pixabay_api_edit = QLineEdit()
        self.pixabay_api_edit.setPlaceholderText("请输入Pixabay API密钥")
        self.pixabay_api_edit.setEchoMode(QLineEdit.Password)
        pixabay_layout.addWidget(self.pixabay_api_edit, 0, 1)
        
        pixabay_help_label = QLabel(
            '<a href="https://pixabay.com/api/docs/">获取Pixabay API密钥</a>'
        )
        pixabay_help_label.setOpenExternalLinks(True)
        pixabay_layout.addWidget(pixabay_help_label, 1, 1)
        
        layout.addWidget(pixabay_group)
        
        # 说明
        info_label = QLabel(
            "注意：\n"
            "• Pexels API密钥是免费的，每月有200次请求限制\n"
            "• Pixabay API密钥是免费的，每分钟有5次请求限制\n"
            "• 如果不设置API密钥，将无法下载视频素材"
        )
        info_label.setWordWrap(True)
        info_label.setStyleSheet("color: #666; font-size: 12px;")
        layout.addWidget(info_label)
        
        layout.addStretch()
        return widget

    def create_video_tab(self) -> QWidget:
        """创建视频设置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 视频质量设置
        quality_group = QGroupBox("视频质量设置")
        quality_layout = QGridLayout(quality_group)

        # 分辨率
        quality_layout.addWidget(QLabel("分辨率:"), 0, 0)
        self.resolution_combo = QComboBox()
        self.resolution_combo.addItems([
            "1920x1080 (1080p)",
            "1280x720 (720p)",
            "854x480 (480p)",
            "640x360 (360p)"
        ])
        quality_layout.addWidget(self.resolution_combo, 0, 1)

        # 帧率
        quality_layout.addWidget(QLabel("帧率:"), 1, 0)
        self.fps_combo = QComboBox()
        self.fps_combo.addItems(["30", "25", "24", "60"])
        quality_layout.addWidget(self.fps_combo, 1, 1)

        # 视频码率
        quality_layout.addWidget(QLabel("视频码率:"), 2, 0)
        self.video_bitrate_combo = QComboBox()
        self.video_bitrate_combo.addItems([
            "5000k (高质量)",
            "3000k (中等质量)",
            "1500k (标准质量)",
            "800k (低质量)"
        ])
        quality_layout.addWidget(self.video_bitrate_combo, 2, 1)

        # 编码器
        quality_layout.addWidget(QLabel("视频编码器:"), 3, 0)
        self.video_codec_combo = QComboBox()
        self.video_codec_combo.addItems([
            "libx264 (H.264)",
            "libx265 (H.265/HEVC)",
            "libvpx-vp9 (VP9)"
        ])
        quality_layout.addWidget(self.video_codec_combo, 3, 1)

        # 编码预设
        quality_layout.addWidget(QLabel("编码预设:"), 4, 0)
        self.preset_combo = QComboBox()
        self.preset_combo.addItems([
            "fast (快速)",
            "medium (中等)",
            "slow (慢速，高质量)",
            "veryslow (极慢，最高质量)"
        ])
        quality_layout.addWidget(self.preset_combo, 4, 1)

        layout.addWidget(quality_group)

        # 视频处理设置
        processing_group = QGroupBox("视频处理设置")
        processing_layout = QGridLayout(processing_group)

        # 视频片段最大长度
        processing_layout.addWidget(QLabel("片段最大长度(秒):"), 0, 0)
        self.max_clip_duration_spin = QSpinBox()
        self.max_clip_duration_spin.setRange(5, 60)
        self.max_clip_duration_spin.setValue(30)
        processing_layout.addWidget(self.max_clip_duration_spin, 0, 1)

        # 视频片段最小长度
        processing_layout.addWidget(QLabel("片段最小长度(秒):"), 1, 0)
        self.min_clip_duration_spin = QSpinBox()
        self.min_clip_duration_spin.setRange(1, 30)
        self.min_clip_duration_spin.setValue(3)
        processing_layout.addWidget(self.min_clip_duration_spin, 1, 1)

        # 视频缩放模式
        processing_layout.addWidget(QLabel("缩放模式:"), 2, 0)
        self.scale_mode_combo = QComboBox()
        self.scale_mode_combo.addItems([
            "保持比例，填充黑边",
            "保持比例，裁剪适应",
            "拉伸填充"
        ])
        processing_layout.addWidget(self.scale_mode_combo, 2, 1)

        layout.addWidget(processing_group)
        layout.addStretch()

        return widget

    def create_audio_tab(self) -> QWidget:
        """创建音频设置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 音频质量设置
        audio_group = QGroupBox("音频质量设置")
        audio_layout = QGridLayout(audio_group)

        # 音频码率
        audio_layout.addWidget(QLabel("音频码率:"), 0, 0)
        self.audio_bitrate_combo = QComboBox()
        self.audio_bitrate_combo.addItems([
            "320k (最高质量)",
            "256k (高质量)",
            "192k (标准质量)",
            "128k (中等质量)",
            "96k (低质量)"
        ])
        audio_layout.addWidget(self.audio_bitrate_combo, 0, 1)

        # 采样率
        audio_layout.addWidget(QLabel("采样率:"), 1, 0)
        self.sample_rate_combo = QComboBox()
        self.sample_rate_combo.addItems([
            "48000 Hz",
            "44100 Hz",
            "32000 Hz",
            "22050 Hz"
        ])
        audio_layout.addWidget(self.sample_rate_combo, 1, 1)

        # 声道数
        audio_layout.addWidget(QLabel("声道数:"), 2, 0)
        self.channels_combo = QComboBox()
        self.channels_combo.addItems([
            "2 (立体声)",
            "1 (单声道)"
        ])
        audio_layout.addWidget(self.channels_combo, 2, 1)

        # 音频编码器
        audio_layout.addWidget(QLabel("音频编码器:"), 3, 0)
        self.audio_codec_combo = QComboBox()
        self.audio_codec_combo.addItems([
            "aac (推荐)",
            "mp3",
            "opus"
        ])
        audio_layout.addWidget(self.audio_codec_combo, 3, 1)

        layout.addWidget(audio_group)

        # 音频处理设置
        audio_processing_group = QGroupBox("音频处理设置")
        audio_processing_layout = QGridLayout(audio_processing_group)

        # 音量调节
        audio_processing_layout.addWidget(QLabel("音量调节:"), 0, 0)
        self.volume_spin = QSpinBox()
        self.volume_spin.setRange(50, 200)
        self.volume_spin.setValue(100)
        self.volume_spin.setSuffix("%")
        audio_processing_layout.addWidget(self.volume_spin, 0, 1)

        # 音频淡入淡出
        audio_processing_layout.addWidget(QLabel("淡入时间(秒):"), 1, 0)
        self.fade_in_spin = QSpinBox()
        self.fade_in_spin.setRange(0, 10)
        self.fade_in_spin.setValue(1)
        audio_processing_layout.addWidget(self.fade_in_spin, 1, 1)

        audio_processing_layout.addWidget(QLabel("淡出时间(秒):"), 2, 0)
        self.fade_out_spin = QSpinBox()
        self.fade_out_spin.setRange(0, 10)
        self.fade_out_spin.setValue(2)
        audio_processing_layout.addWidget(self.fade_out_spin, 2, 1)

        layout.addWidget(audio_processing_group)
        layout.addStretch()

        return widget

    def create_subtitle_tab(self) -> QWidget:
        """创建字幕设置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 字体设置
        font_group = QGroupBox("字体设置")
        font_layout = QGridLayout(font_group)

        # 字体大小
        font_layout.addWidget(QLabel("字体大小:"), 0, 0)
        self.subtitle_font_size_spin = QSpinBox()
        self.subtitle_font_size_spin.setRange(12, 72)
        self.subtitle_font_size_spin.setValue(24)
        font_layout.addWidget(self.subtitle_font_size_spin, 0, 1)

        # 字体族
        font_layout.addWidget(QLabel("字体族:"), 1, 0)
        self.subtitle_font_family_combo = QComboBox()
        self.subtitle_font_family_combo.addItems([
            "Microsoft YaHei",
            "SimHei",
            "Arial",
            "Times New Roman",
            "Courier New"
        ])
        font_layout.addWidget(self.subtitle_font_family_combo, 1, 1)

        # 字体颜色
        font_layout.addWidget(QLabel("字体颜色:"), 2, 0)
        self.subtitle_font_color_btn = QPushButton("#FFFFFF")
        self.subtitle_font_color_btn.clicked.connect(lambda: self._choose_color(self.subtitle_font_color_btn))
        font_layout.addWidget(self.subtitle_font_color_btn, 2, 1)

        # 字体粗细
        font_layout.addWidget(QLabel("字体粗细:"), 3, 0)
        self.subtitle_font_weight_combo = QComboBox()
        self.subtitle_font_weight_combo.addItems(["normal", "bold"])
        font_layout.addWidget(self.subtitle_font_weight_combo, 3, 1)

        layout.addWidget(font_group)

        # 描边和阴影设置
        outline_group = QGroupBox("描边和阴影")
        outline_layout = QGridLayout(outline_group)

        # 描边颜色
        outline_layout.addWidget(QLabel("描边颜色:"), 0, 0)
        self.subtitle_outline_color_btn = QPushButton("#000000")
        self.subtitle_outline_color_btn.clicked.connect(lambda: self._choose_color(self.subtitle_outline_color_btn))
        outline_layout.addWidget(self.subtitle_outline_color_btn, 0, 1)

        # 描边宽度
        outline_layout.addWidget(QLabel("描边宽度:"), 1, 0)
        self.subtitle_outline_width_spin = QSpinBox()
        self.subtitle_outline_width_spin.setRange(0, 10)
        self.subtitle_outline_width_spin.setValue(2)
        outline_layout.addWidget(self.subtitle_outline_width_spin, 1, 1)

        # 阴影
        outline_layout.addWidget(QLabel("阴影强度:"), 2, 0)
        self.subtitle_shadow_spin = QSpinBox()
        self.subtitle_shadow_spin.setRange(0, 5)
        self.subtitle_shadow_spin.setValue(1)
        outline_layout.addWidget(self.subtitle_shadow_spin, 2, 1)

        layout.addWidget(outline_group)

        # 位置和显示设置
        position_group = QGroupBox("位置和显示")
        position_layout = QGridLayout(position_group)

        # 位置
        position_layout.addWidget(QLabel("位置:"), 0, 0)
        self.subtitle_position_combo = QComboBox()
        self.subtitle_position_combo.addItems(["top", "center", "bottom"])
        position_layout.addWidget(self.subtitle_position_combo, 0, 1)

        # 对齐方式
        position_layout.addWidget(QLabel("对齐:"), 1, 0)
        self.subtitle_alignment_combo = QComboBox()
        self.subtitle_alignment_combo.addItems([
            "1 (左对齐)",
            "2 (居中)",
            "3 (右对齐)"
        ])
        position_layout.addWidget(self.subtitle_alignment_combo, 1, 1)

        # 垂直边距
        position_layout.addWidget(QLabel("垂直边距:"), 2, 0)
        self.subtitle_margin_v_spin = QSpinBox()
        self.subtitle_margin_v_spin.setRange(0, 200)
        self.subtitle_margin_v_spin.setValue(50)
        position_layout.addWidget(self.subtitle_margin_v_spin, 2, 1)

        # 最大行数
        position_layout.addWidget(QLabel("最大行数:"), 3, 0)
        self.subtitle_max_lines_spin = QSpinBox()
        self.subtitle_max_lines_spin.setRange(1, 5)
        self.subtitle_max_lines_spin.setValue(2)
        position_layout.addWidget(self.subtitle_max_lines_spin, 3, 1)

        layout.addWidget(position_group)

        # 显示模式设置
        mode_group = QGroupBox("显示模式")
        mode_layout = QGridLayout(mode_group)

        # 显示模式
        mode_layout.addWidget(QLabel("显示模式:"), 0, 0)
        self.subtitle_display_mode_combo = QComboBox()
        self.subtitle_display_mode_combo.addItems([
            "static (静态显示)",
            "scroll (滚动显示)",
            "karaoke (卡拉OK模式)"
        ])
        mode_layout.addWidget(self.subtitle_display_mode_combo, 0, 1)

        # 滚动速度
        mode_layout.addWidget(QLabel("滚动速度:"), 1, 0)
        self.subtitle_scroll_speed_spin = QDoubleSpinBox()
        self.subtitle_scroll_speed_spin.setRange(0.1, 5.0)
        self.subtitle_scroll_speed_spin.setValue(1.0)
        self.subtitle_scroll_speed_spin.setSingleStep(0.1)
        mode_layout.addWidget(self.subtitle_scroll_speed_spin, 1, 1)

        # 滚动方向
        mode_layout.addWidget(QLabel("滚动方向:"), 2, 0)
        self.subtitle_scroll_direction_combo = QComboBox()
        self.subtitle_scroll_direction_combo.addItems(["up", "down", "left", "right"])
        mode_layout.addWidget(self.subtitle_scroll_direction_combo, 2, 1)

        layout.addWidget(mode_group)
        layout.addStretch()

        return widget

    def create_advanced_tab(self) -> QWidget:
        """创建高级设置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 抽帧设置
        frame_group = QGroupBox("视频抽帧设置")
        frame_layout = QGridLayout(frame_group)

        # 启用抽帧
        frame_layout.addWidget(QLabel("启用抽帧:"), 0, 0)
        self.frame_extract_enabled_check = QCheckBox()
        frame_layout.addWidget(self.frame_extract_enabled_check, 0, 1)

        # 抽帧间隔
        frame_layout.addWidget(QLabel("抽帧间隔(秒):"), 1, 0)
        self.frame_extract_interval_spin = QDoubleSpinBox()
        self.frame_extract_interval_spin.setRange(0.5, 60.0)
        self.frame_extract_interval_spin.setValue(5.0)
        self.frame_extract_interval_spin.setSingleStep(0.5)
        frame_layout.addWidget(self.frame_extract_interval_spin, 1, 1)

        # 抽帧数量
        frame_layout.addWidget(QLabel("每次抽帧数:"), 2, 0)
        self.frame_extract_count_spin = QSpinBox()
        self.frame_extract_count_spin.setRange(1, 10)
        self.frame_extract_count_spin.setValue(3)
        frame_layout.addWidget(self.frame_extract_count_spin, 2, 1)

        # 抽帧质量
        frame_layout.addWidget(QLabel("抽帧质量:"), 3, 0)
        self.frame_extract_quality_combo = QComboBox()
        self.frame_extract_quality_combo.addItems([
            "low (低质量，小文件)",
            "medium (中等质量)",
            "high (高质量，大文件)"
        ])
        frame_layout.addWidget(self.frame_extract_quality_combo, 3, 1)

        layout.addWidget(frame_group)

        # 性能设置
        performance_group = QGroupBox("性能设置")
        performance_layout = QGridLayout(performance_group)

        # 并发下载数
        performance_layout.addWidget(QLabel("并发下载数:"), 0, 0)
        self.max_concurrent_downloads_spin = QSpinBox()
        self.max_concurrent_downloads_spin.setRange(1, 10)
        self.max_concurrent_downloads_spin.setValue(5)
        performance_layout.addWidget(self.max_concurrent_downloads_spin, 0, 1)

        # 自动清理
        performance_layout.addWidget(QLabel("自动清理临时文件:"), 1, 0)
        self.auto_cleanup_check = QCheckBox()
        self.auto_cleanup_check.setChecked(True)
        performance_layout.addWidget(self.auto_cleanup_check, 1, 1)

        layout.addWidget(performance_group)
        layout.addStretch()

        return widget

    def _choose_color(self, button: QPushButton):
        """选择颜色"""
        color = QColorDialog.getColor()
        if color.isValid():
            color_hex = color.name()
            button.setText(color_hex)
            button.setStyleSheet(f"background-color: {color_hex}; color: {'white' if color.lightness() < 128 else 'black'};")
    

    
    def load_settings(self):
        """加载设置"""
        # 加载API密钥
        self.pexels_api_edit.setText(config.get_api_key("pexels"))
        self.pixabay_api_edit.setText(config.get_api_key("pixabay"))

        # 加载视频设置
        video_config = config.video

        # 分辨率
        resolution_text = f"{video_config.width}x{video_config.height}"
        for i in range(self.resolution_combo.count()):
            if resolution_text in self.resolution_combo.itemText(i):
                self.resolution_combo.setCurrentIndex(i)
                break

        # 帧率
        fps_text = str(video_config.fps)
        for i in range(self.fps_combo.count()):
            if self.fps_combo.itemText(i) == fps_text:
                self.fps_combo.setCurrentIndex(i)
                break

        # 视频码率
        bitrate_text = f"{video_config.bitrate}k"
        for i in range(self.video_bitrate_combo.count()):
            if bitrate_text in self.video_bitrate_combo.itemText(i):
                self.video_bitrate_combo.setCurrentIndex(i)
                break

        # 编码器
        codec_text = video_config.codec
        for i in range(self.video_codec_combo.count()):
            if codec_text in self.video_codec_combo.itemText(i):
                self.video_codec_combo.setCurrentIndex(i)
                break

        # 预设
        preset_text = video_config.preset
        for i in range(self.preset_combo.count()):
            if preset_text in self.preset_combo.itemText(i):
                self.preset_combo.setCurrentIndex(i)
                break

        # 视频处理设置
        self.max_clip_duration_spin.setValue(config.app_config.get('max_clip_duration', 30))
        self.min_clip_duration_spin.setValue(config.app_config.get('min_clip_duration', 3))

        scale_mode = config.app_config.get('scale_mode', 0)
        self.scale_mode_combo.setCurrentIndex(scale_mode)

        # 加载音频设置
        audio_config = config.audio

        # 音频码率
        audio_bitrate_text = f"{audio_config.bitrate}k"
        for i in range(self.audio_bitrate_combo.count()):
            if audio_bitrate_text in self.audio_bitrate_combo.itemText(i):
                self.audio_bitrate_combo.setCurrentIndex(i)
                break

        # 采样率
        sample_rate_text = f"{audio_config.sample_rate} Hz"
        for i in range(self.sample_rate_combo.count()):
            if sample_rate_text in self.sample_rate_combo.itemText(i):
                self.sample_rate_combo.setCurrentIndex(i)
                break

        # 声道数
        channels_text = f"{audio_config.channels}"
        for i in range(self.channels_combo.count()):
            if channels_text in self.channels_combo.itemText(i):
                self.channels_combo.setCurrentIndex(i)
                break

        # 音频编码器
        audio_codec_text = audio_config.codec
        for i in range(self.audio_codec_combo.count()):
            if audio_codec_text in self.audio_codec_combo.itemText(i):
                self.audio_codec_combo.setCurrentIndex(i)
                break

        # 音频处理设置
        self.volume_spin.setValue(config.app_config.get('audio_volume', 100))
        self.fade_in_spin.setValue(config.app_config.get('fade_in_duration', 1))
        self.fade_out_spin.setValue(config.app_config.get('fade_out_duration', 2))

        # 加载字幕设置
        subtitle_config = config.subtitle

        self.subtitle_font_size_spin.setValue(subtitle_config.font_size)

        # 字体族
        for i in range(self.subtitle_font_family_combo.count()):
            if self.subtitle_font_family_combo.itemText(i) == subtitle_config.font_family:
                self.subtitle_font_family_combo.setCurrentIndex(i)
                break

        # 字体颜色
        self.subtitle_font_color_btn.setText(subtitle_config.font_color)
        self.subtitle_font_color_btn.setStyleSheet(f"background-color: {subtitle_config.font_color};")

        # 字体粗细
        for i in range(self.subtitle_font_weight_combo.count()):
            if self.subtitle_font_weight_combo.itemText(i) == subtitle_config.font_weight:
                self.subtitle_font_weight_combo.setCurrentIndex(i)
                break

        # 描边颜色
        self.subtitle_outline_color_btn.setText(subtitle_config.outline_color)
        self.subtitle_outline_color_btn.setStyleSheet(f"background-color: {subtitle_config.outline_color};")

        self.subtitle_outline_width_spin.setValue(subtitle_config.outline_width)
        self.subtitle_shadow_spin.setValue(subtitle_config.shadow)

        # 位置设置
        for i in range(self.subtitle_position_combo.count()):
            if self.subtitle_position_combo.itemText(i) == subtitle_config.position:
                self.subtitle_position_combo.setCurrentIndex(i)
                break

        # 对齐方式
        alignment_index = subtitle_config.alignment - 1  # 转换为索引
        if 0 <= alignment_index < self.subtitle_alignment_combo.count():
            self.subtitle_alignment_combo.setCurrentIndex(alignment_index)

        self.subtitle_margin_v_spin.setValue(subtitle_config.margin_v)
        self.subtitle_max_lines_spin.setValue(subtitle_config.max_lines)

        # 显示模式
        for i in range(self.subtitle_display_mode_combo.count()):
            if subtitle_config.display_mode in self.subtitle_display_mode_combo.itemText(i):
                self.subtitle_display_mode_combo.setCurrentIndex(i)
                break

        self.subtitle_scroll_speed_spin.setValue(subtitle_config.scroll_speed)

        for i in range(self.subtitle_scroll_direction_combo.count()):
            if self.subtitle_scroll_direction_combo.itemText(i) == subtitle_config.scroll_direction:
                self.subtitle_scroll_direction_combo.setCurrentIndex(i)
                break

        # 加载高级设置
        self.frame_extract_enabled_check.setChecked(config.app_config.get('frame_extract_enabled', False))
        self.frame_extract_interval_spin.setValue(config.app_config.get('frame_extract_interval', 5.0))
        self.frame_extract_count_spin.setValue(config.app_config.get('frame_extract_count', 3))

        quality_text = config.app_config.get('frame_extract_quality', 'high')
        for i in range(self.frame_extract_quality_combo.count()):
            if quality_text in self.frame_extract_quality_combo.itemText(i):
                self.frame_extract_quality_combo.setCurrentIndex(i)
                break

        self.max_concurrent_downloads_spin.setValue(config.app_config.get('max_concurrent_downloads', 5))
        self.auto_cleanup_check.setChecked(config.app_config.get('auto_cleanup', True))
    
    def save_settings(self):
        """保存设置"""
        try:
            # 保存API密钥
            pexels_key = self.pexels_api_edit.text().strip()
            pixabay_key = self.pixabay_api_edit.text().strip()

            if pexels_key:
                config.update_api_key("pexels", pexels_key)
            if pixabay_key:
                config.update_api_key("pixabay", pixabay_key)

            # 保存视频设置
            resolution_text = self.resolution_combo.currentText()
            if "1920x1080" in resolution_text:
                config.video.width, config.video.height = 1920, 1080
            elif "1280x720" in resolution_text:
                config.video.width, config.video.height = 1280, 720
            elif "854x480" in resolution_text:
                config.video.width, config.video.height = 854, 480
            elif "640x360" in resolution_text:
                config.video.width, config.video.height = 640, 360

            config.video.fps = int(self.fps_combo.currentText())

            # 视频码率
            bitrate_text = self.video_bitrate_combo.currentText()
            if "5000k" in bitrate_text:
                config.video.bitrate = 5000
            elif "3000k" in bitrate_text:
                config.video.bitrate = 3000
            elif "1500k" in bitrate_text:
                config.video.bitrate = 1500
            elif "800k" in bitrate_text:
                config.video.bitrate = 800

            # 视频编码器
            codec_text = self.video_codec_combo.currentText()
            if "libx264" in codec_text:
                config.video.codec = "libx264"
            elif "libx265" in codec_text:
                config.video.codec = "libx265"
            elif "libvpx-vp9" in codec_text:
                config.video.codec = "libvpx-vp9"

            # 编码预设
            preset_text = self.preset_combo.currentText()
            if "fast" in preset_text:
                config.video.preset = "fast"
            elif "medium" in preset_text:
                config.video.preset = "medium"
            elif "slow" in preset_text:
                config.video.preset = "slow"
            elif "veryslow" in preset_text:
                config.video.preset = "veryslow"

            # 视频处理设置
            config.app_config['max_clip_duration'] = self.max_clip_duration_spin.value()
            config.app_config['min_clip_duration'] = self.min_clip_duration_spin.value()
            config.app_config['scale_mode'] = self.scale_mode_combo.currentIndex()

            # 保存音频设置
            audio_bitrate_text = self.audio_bitrate_combo.currentText()
            if "320k" in audio_bitrate_text:
                config.audio.bitrate = 320
            elif "256k" in audio_bitrate_text:
                config.audio.bitrate = 256
            elif "192k" in audio_bitrate_text:
                config.audio.bitrate = 192
            elif "128k" in audio_bitrate_text:
                config.audio.bitrate = 128
            elif "96k" in audio_bitrate_text:
                config.audio.bitrate = 96

            # 采样率
            sample_rate_text = self.sample_rate_combo.currentText()
            config.audio.sample_rate = int(sample_rate_text.split()[0])

            # 声道数
            channels_text = self.channels_combo.currentText()
            config.audio.channels = int(channels_text.split()[0])

            # 音频编码器
            audio_codec_text = self.audio_codec_combo.currentText()
            config.audio.codec = audio_codec_text.split()[0]

            # 音频处理设置
            config.app_config['audio_volume'] = self.volume_spin.value()
            config.app_config['fade_in_duration'] = self.fade_in_spin.value()
            config.app_config['fade_out_duration'] = self.fade_out_spin.value()

            # 保存字幕设置
            config.subtitle.font_size = self.subtitle_font_size_spin.value()
            config.subtitle.font_family = self.subtitle_font_family_combo.currentText()
            config.subtitle.font_color = self.subtitle_font_color_btn.text()
            config.subtitle.font_weight = self.subtitle_font_weight_combo.currentText()

            config.subtitle.outline_color = self.subtitle_outline_color_btn.text()
            config.subtitle.outline_width = self.subtitle_outline_width_spin.value()
            config.subtitle.shadow = self.subtitle_shadow_spin.value()

            config.subtitle.position = self.subtitle_position_combo.currentText()
            config.subtitle.alignment = self.subtitle_alignment_combo.currentIndex() + 1  # 转换为ASS格式
            config.subtitle.margin_v = self.subtitle_margin_v_spin.value()
            config.subtitle.max_lines = self.subtitle_max_lines_spin.value()

            # 显示模式
            display_mode_text = self.subtitle_display_mode_combo.currentText()
            if "static" in display_mode_text:
                config.subtitle.display_mode = "static"
            elif "scroll" in display_mode_text:
                config.subtitle.display_mode = "scroll"
            elif "karaoke" in display_mode_text:
                config.subtitle.display_mode = "karaoke"

            config.subtitle.scroll_speed = self.subtitle_scroll_speed_spin.value()
            config.subtitle.scroll_direction = self.subtitle_scroll_direction_combo.currentText()

            # 保存高级设置
            config.app_config['frame_extract_enabled'] = self.frame_extract_enabled_check.isChecked()
            config.app_config['frame_extract_interval'] = self.frame_extract_interval_spin.value()
            config.app_config['frame_extract_count'] = self.frame_extract_count_spin.value()

            quality_text = self.frame_extract_quality_combo.currentText()
            if "low" in quality_text:
                config.app_config['frame_extract_quality'] = "low"
            elif "medium" in quality_text:
                config.app_config['frame_extract_quality'] = "medium"
            else:
                config.app_config['frame_extract_quality'] = "high"

            config.app_config['max_concurrent_downloads'] = self.max_concurrent_downloads_spin.value()
            config.app_config['auto_cleanup'] = self.auto_cleanup_check.isChecked()

            # 保存配置到文件
            config.save_config()

            QMessageBox.information(self, "成功", "设置已保存")
            self.accept()

        except Exception as e:
            self.logger.error(f"保存设置失败: {e}")
            QMessageBox.critical(self, "错误", f"保存设置失败：{e}")
    
    def show_api_help(self):
        """显示API帮助"""
        help_text = """
API密钥获取方法：

Pexels API:
1. 访问 https://www.pexels.com/api/
2. 注册账号并登录
3. 创建应用程序获取API密钥
4. 免费版本每月200次请求

Pixabay API:
1. 访问 https://pixabay.com/api/docs/
2. 注册账号并登录
3. 在账号设置中获取API密钥
4. 免费版本每分钟5次请求
        """
        
        QMessageBox.information(self, "API帮助", help_text)
