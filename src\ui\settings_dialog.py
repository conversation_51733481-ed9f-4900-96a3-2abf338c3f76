"""
设置对话框模块
提供API密钥配置和其他应用程序设置的界面
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QLineEdit, QPushButton, QTabWidget,
    QWidget, QGroupBox, QMessageBox, QComboBox
)
from PySide6.QtCore import Qt

from src.config.settings import config
from src.theme.theme_manager import theme_manager
from src.logger.logger import get_logger


class SettingsDialog(QDialog):
    """设置对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger("SettingsDialog")
        self.init_ui()
        self.load_settings()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("设置")
        self.setModal(True)
        self.resize(500, 400)
        
        layout = QVBoxLayout(self)
        
        # 创建选项卡
        tab_widget = QTabWidget()
        layout.addWidget(tab_widget)
        
        # API设置选项卡
        api_tab = self.create_api_tab()
        tab_widget.addTab(api_tab, "API设置")
        
        # 主题设置选项卡
        theme_tab = self.create_theme_tab()
        tab_widget.addTab(theme_tab, "主题设置")
        
        # 按钮
        button_layout = QHBoxLayout()
        
        self.save_button = QPushButton("保存")
        self.save_button.clicked.connect(self.save_settings)
        button_layout.addWidget(self.save_button)
        
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
    
    def create_api_tab(self) -> QWidget:
        """创建API设置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Pexels API设置
        pexels_group = QGroupBox("Pexels API")
        pexels_layout = QGridLayout(pexels_group)
        
        pexels_layout.addWidget(QLabel("API密钥:"), 0, 0)
        self.pexels_api_edit = QLineEdit()
        self.pexels_api_edit.setPlaceholderText("请输入Pexels API密钥")
        self.pexels_api_edit.setEchoMode(QLineEdit.Password)
        pexels_layout.addWidget(self.pexels_api_edit, 0, 1)
        
        pexels_help_label = QLabel(
            '<a href="https://www.pexels.com/api/">获取Pexels API密钥</a>'
        )
        pexels_help_label.setOpenExternalLinks(True)
        pexels_layout.addWidget(pexels_help_label, 1, 1)
        
        layout.addWidget(pexels_group)
        
        # Pixabay API设置
        pixabay_group = QGroupBox("Pixabay API")
        pixabay_layout = QGridLayout(pixabay_group)
        
        pixabay_layout.addWidget(QLabel("API密钥:"), 0, 0)
        self.pixabay_api_edit = QLineEdit()
        self.pixabay_api_edit.setPlaceholderText("请输入Pixabay API密钥")
        self.pixabay_api_edit.setEchoMode(QLineEdit.Password)
        pixabay_layout.addWidget(self.pixabay_api_edit, 0, 1)
        
        pixabay_help_label = QLabel(
            '<a href="https://pixabay.com/api/docs/">获取Pixabay API密钥</a>'
        )
        pixabay_help_label.setOpenExternalLinks(True)
        pixabay_layout.addWidget(pixabay_help_label, 1, 1)
        
        layout.addWidget(pixabay_group)
        
        # 说明
        info_label = QLabel(
            "注意：\n"
            "• Pexels API密钥是免费的，每月有200次请求限制\n"
            "• Pixabay API密钥是免费的，每分钟有5次请求限制\n"
            "• 如果不设置API密钥，将无法下载视频素材"
        )
        info_label.setWordWrap(True)
        info_label.setStyleSheet("color: #666; font-size: 12px;")
        layout.addWidget(info_label)
        
        layout.addStretch()
        return widget
    
    def create_theme_tab(self) -> QWidget:
        """创建主题设置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        theme_group = QGroupBox("界面主题")
        theme_layout = QGridLayout(theme_group)
        
        theme_layout.addWidget(QLabel("选择主题:"), 0, 0)
        self.theme_combo = QComboBox()
        
        # 添加可用主题
        themes = theme_manager.get_available_themes()
        theme_names = {
            "light": "浅色主题",
            "dark": "深色主题", 
            "blue": "蓝色主题"
        }
        
        for theme in themes:
            display_name = theme_names.get(theme, theme)
            self.theme_combo.addItem(display_name, theme)
        
        theme_layout.addWidget(self.theme_combo, 0, 1)
        
        layout.addWidget(theme_group)
        layout.addStretch()
        
        return widget
    
    def load_settings(self):
        """加载设置"""
        # 加载API密钥
        self.pexels_api_edit.setText(config.get_api_key("pexels"))
        self.pixabay_api_edit.setText(config.get_api_key("pixabay"))
        
        # 加载主题设置
        current_theme = theme_manager.get_current_theme()
        for i in range(self.theme_combo.count()):
            if self.theme_combo.itemData(i) == current_theme:
                self.theme_combo.setCurrentIndex(i)
                break
    
    def save_settings(self):
        """保存设置"""
        try:
            # 保存API密钥
            pexels_key = self.pexels_api_edit.text().strip()
            pixabay_key = self.pixabay_api_edit.text().strip()
            
            if pexels_key:
                config.update_api_key("pexels", pexels_key)
            if pixabay_key:
                config.update_api_key("pixabay", pixabay_key)
            
            # 保存主题设置
            selected_theme = self.theme_combo.currentData()
            if selected_theme:
                theme_manager.set_theme(selected_theme)
                config.app_config['theme'] = selected_theme
                config.save_config()
            
            QMessageBox.information(self, "成功", "设置已保存")
            self.accept()
            
        except Exception as e:
            self.logger.error(f"保存设置失败: {e}")
            QMessageBox.critical(self, "错误", f"保存设置失败：{e}")
    
    def show_api_help(self):
        """显示API帮助"""
        help_text = """
API密钥获取方法：

Pexels API:
1. 访问 https://www.pexels.com/api/
2. 注册账号并登录
3. 创建应用程序获取API密钥
4. 免费版本每月200次请求

Pixabay API:
1. 访问 https://pixabay.com/api/docs/
2. 注册账号并登录
3. 在账号设置中获取API密钥
4. 免费版本每分钟5次请求
        """
        
        QMessageBox.information(self, "API帮助", help_text)
