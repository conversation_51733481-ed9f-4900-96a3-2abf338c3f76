"""
设置对话框模块
提供API密钥配置和其他应用程序设置的界面
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QLineEdit, QPushButton, QTabWidget,
    QWidget, QGroupBox, QMessageBox, QComboBox, QSpinBox
)
from PySide6.QtCore import Qt

from src.config.settings import config
from src.theme.theme_manager import theme_manager
from src.logger.logger import get_logger


class SettingsDialog(QDialog):
    """设置对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger("SettingsDialog")
        self.init_ui()
        self.load_settings()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("设置")
        self.setModal(True)
        self.resize(500, 400)
        
        layout = QVBoxLayout(self)
        
        # 创建选项卡
        tab_widget = QTabWidget()
        layout.addWidget(tab_widget)
        
        # API设置选项卡
        api_tab = self.create_api_tab()
        tab_widget.addTab(api_tab, "API设置")

        # 视频设置选项卡
        video_tab = self.create_video_tab()
        tab_widget.addTab(video_tab, "视频设置")

        # 音频设置选项卡
        audio_tab = self.create_audio_tab()
        tab_widget.addTab(audio_tab, "音频设置")
        
        # 按钮
        button_layout = QHBoxLayout()
        
        self.save_button = QPushButton("保存")
        self.save_button.clicked.connect(self.save_settings)
        button_layout.addWidget(self.save_button)
        
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
    
    def create_api_tab(self) -> QWidget:
        """创建API设置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Pexels API设置
        pexels_group = QGroupBox("Pexels API")
        pexels_layout = QGridLayout(pexels_group)
        
        pexels_layout.addWidget(QLabel("API密钥:"), 0, 0)
        self.pexels_api_edit = QLineEdit()
        self.pexels_api_edit.setPlaceholderText("请输入Pexels API密钥")
        self.pexels_api_edit.setEchoMode(QLineEdit.Password)
        pexels_layout.addWidget(self.pexels_api_edit, 0, 1)
        
        pexels_help_label = QLabel(
            '<a href="https://www.pexels.com/api/">获取Pexels API密钥</a>'
        )
        pexels_help_label.setOpenExternalLinks(True)
        pexels_layout.addWidget(pexels_help_label, 1, 1)
        
        layout.addWidget(pexels_group)
        
        # Pixabay API设置
        pixabay_group = QGroupBox("Pixabay API")
        pixabay_layout = QGridLayout(pixabay_group)
        
        pixabay_layout.addWidget(QLabel("API密钥:"), 0, 0)
        self.pixabay_api_edit = QLineEdit()
        self.pixabay_api_edit.setPlaceholderText("请输入Pixabay API密钥")
        self.pixabay_api_edit.setEchoMode(QLineEdit.Password)
        pixabay_layout.addWidget(self.pixabay_api_edit, 0, 1)
        
        pixabay_help_label = QLabel(
            '<a href="https://pixabay.com/api/docs/">获取Pixabay API密钥</a>'
        )
        pixabay_help_label.setOpenExternalLinks(True)
        pixabay_layout.addWidget(pixabay_help_label, 1, 1)
        
        layout.addWidget(pixabay_group)
        
        # 说明
        info_label = QLabel(
            "注意：\n"
            "• Pexels API密钥是免费的，每月有200次请求限制\n"
            "• Pixabay API密钥是免费的，每分钟有5次请求限制\n"
            "• 如果不设置API密钥，将无法下载视频素材"
        )
        info_label.setWordWrap(True)
        info_label.setStyleSheet("color: #666; font-size: 12px;")
        layout.addWidget(info_label)
        
        layout.addStretch()
        return widget

    def create_video_tab(self) -> QWidget:
        """创建视频设置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 视频质量设置
        quality_group = QGroupBox("视频质量设置")
        quality_layout = QGridLayout(quality_group)

        # 分辨率
        quality_layout.addWidget(QLabel("分辨率:"), 0, 0)
        self.resolution_combo = QComboBox()
        self.resolution_combo.addItems([
            "1920x1080 (1080p)",
            "1280x720 (720p)",
            "854x480 (480p)",
            "640x360 (360p)"
        ])
        quality_layout.addWidget(self.resolution_combo, 0, 1)

        # 帧率
        quality_layout.addWidget(QLabel("帧率:"), 1, 0)
        self.fps_combo = QComboBox()
        self.fps_combo.addItems(["30", "25", "24", "60"])
        quality_layout.addWidget(self.fps_combo, 1, 1)

        # 视频码率
        quality_layout.addWidget(QLabel("视频码率:"), 2, 0)
        self.video_bitrate_combo = QComboBox()
        self.video_bitrate_combo.addItems([
            "5000k (高质量)",
            "3000k (中等质量)",
            "1500k (标准质量)",
            "800k (低质量)"
        ])
        quality_layout.addWidget(self.video_bitrate_combo, 2, 1)

        # 编码器
        quality_layout.addWidget(QLabel("视频编码器:"), 3, 0)
        self.video_codec_combo = QComboBox()
        self.video_codec_combo.addItems([
            "libx264 (H.264)",
            "libx265 (H.265/HEVC)",
            "libvpx-vp9 (VP9)"
        ])
        quality_layout.addWidget(self.video_codec_combo, 3, 1)

        # 编码预设
        quality_layout.addWidget(QLabel("编码预设:"), 4, 0)
        self.preset_combo = QComboBox()
        self.preset_combo.addItems([
            "fast (快速)",
            "medium (中等)",
            "slow (慢速，高质量)",
            "veryslow (极慢，最高质量)"
        ])
        quality_layout.addWidget(self.preset_combo, 4, 1)

        layout.addWidget(quality_group)

        # 视频处理设置
        processing_group = QGroupBox("视频处理设置")
        processing_layout = QGridLayout(processing_group)

        # 视频片段最大长度
        processing_layout.addWidget(QLabel("片段最大长度(秒):"), 0, 0)
        self.max_clip_duration_spin = QSpinBox()
        self.max_clip_duration_spin.setRange(5, 60)
        self.max_clip_duration_spin.setValue(30)
        processing_layout.addWidget(self.max_clip_duration_spin, 0, 1)

        # 视频片段最小长度
        processing_layout.addWidget(QLabel("片段最小长度(秒):"), 1, 0)
        self.min_clip_duration_spin = QSpinBox()
        self.min_clip_duration_spin.setRange(1, 30)
        self.min_clip_duration_spin.setValue(3)
        processing_layout.addWidget(self.min_clip_duration_spin, 1, 1)

        # 视频缩放模式
        processing_layout.addWidget(QLabel("缩放模式:"), 2, 0)
        self.scale_mode_combo = QComboBox()
        self.scale_mode_combo.addItems([
            "保持比例，填充黑边",
            "保持比例，裁剪适应",
            "拉伸填充"
        ])
        processing_layout.addWidget(self.scale_mode_combo, 2, 1)

        layout.addWidget(processing_group)
        layout.addStretch()

        return widget

    def create_audio_tab(self) -> QWidget:
        """创建音频设置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 音频质量设置
        audio_group = QGroupBox("音频质量设置")
        audio_layout = QGridLayout(audio_group)

        # 音频码率
        audio_layout.addWidget(QLabel("音频码率:"), 0, 0)
        self.audio_bitrate_combo = QComboBox()
        self.audio_bitrate_combo.addItems([
            "320k (最高质量)",
            "256k (高质量)",
            "192k (标准质量)",
            "128k (中等质量)",
            "96k (低质量)"
        ])
        audio_layout.addWidget(self.audio_bitrate_combo, 0, 1)

        # 采样率
        audio_layout.addWidget(QLabel("采样率:"), 1, 0)
        self.sample_rate_combo = QComboBox()
        self.sample_rate_combo.addItems([
            "48000 Hz",
            "44100 Hz",
            "32000 Hz",
            "22050 Hz"
        ])
        audio_layout.addWidget(self.sample_rate_combo, 1, 1)

        # 声道数
        audio_layout.addWidget(QLabel("声道数:"), 2, 0)
        self.channels_combo = QComboBox()
        self.channels_combo.addItems([
            "2 (立体声)",
            "1 (单声道)"
        ])
        audio_layout.addWidget(self.channels_combo, 2, 1)

        # 音频编码器
        audio_layout.addWidget(QLabel("音频编码器:"), 3, 0)
        self.audio_codec_combo = QComboBox()
        self.audio_codec_combo.addItems([
            "aac (推荐)",
            "mp3",
            "opus"
        ])
        audio_layout.addWidget(self.audio_codec_combo, 3, 1)

        layout.addWidget(audio_group)

        # 音频处理设置
        audio_processing_group = QGroupBox("音频处理设置")
        audio_processing_layout = QGridLayout(audio_processing_group)

        # 音量调节
        audio_processing_layout.addWidget(QLabel("音量调节:"), 0, 0)
        self.volume_spin = QSpinBox()
        self.volume_spin.setRange(50, 200)
        self.volume_spin.setValue(100)
        self.volume_spin.setSuffix("%")
        audio_processing_layout.addWidget(self.volume_spin, 0, 1)

        # 音频淡入淡出
        audio_processing_layout.addWidget(QLabel("淡入时间(秒):"), 1, 0)
        self.fade_in_spin = QSpinBox()
        self.fade_in_spin.setRange(0, 10)
        self.fade_in_spin.setValue(1)
        audio_processing_layout.addWidget(self.fade_in_spin, 1, 1)

        audio_processing_layout.addWidget(QLabel("淡出时间(秒):"), 2, 0)
        self.fade_out_spin = QSpinBox()
        self.fade_out_spin.setRange(0, 10)
        self.fade_out_spin.setValue(2)
        audio_processing_layout.addWidget(self.fade_out_spin, 2, 1)

        layout.addWidget(audio_processing_group)
        layout.addStretch()

        return widget
    

    
    def load_settings(self):
        """加载设置"""
        # 加载API密钥
        self.pexels_api_edit.setText(config.get_api_key("pexels"))
        self.pixabay_api_edit.setText(config.get_api_key("pixabay"))

        # 加载视频设置
        video_config = config.video

        # 分辨率
        resolution_text = f"{video_config.width}x{video_config.height}"
        for i in range(self.resolution_combo.count()):
            if resolution_text in self.resolution_combo.itemText(i):
                self.resolution_combo.setCurrentIndex(i)
                break

        # 帧率
        fps_text = str(video_config.fps)
        for i in range(self.fps_combo.count()):
            if self.fps_combo.itemText(i) == fps_text:
                self.fps_combo.setCurrentIndex(i)
                break

        # 视频码率
        bitrate_text = f"{video_config.bitrate}k"
        for i in range(self.video_bitrate_combo.count()):
            if bitrate_text in self.video_bitrate_combo.itemText(i):
                self.video_bitrate_combo.setCurrentIndex(i)
                break

        # 编码器
        codec_text = video_config.codec
        for i in range(self.video_codec_combo.count()):
            if codec_text in self.video_codec_combo.itemText(i):
                self.video_codec_combo.setCurrentIndex(i)
                break

        # 预设
        preset_text = video_config.preset
        for i in range(self.preset_combo.count()):
            if preset_text in self.preset_combo.itemText(i):
                self.preset_combo.setCurrentIndex(i)
                break

        # 视频处理设置
        self.max_clip_duration_spin.setValue(config.app_config.get('max_clip_duration', 30))
        self.min_clip_duration_spin.setValue(config.app_config.get('min_clip_duration', 3))

        scale_mode = config.app_config.get('scale_mode', 0)
        self.scale_mode_combo.setCurrentIndex(scale_mode)

        # 加载音频设置
        audio_config = config.audio

        # 音频码率
        audio_bitrate_text = f"{audio_config.bitrate}k"
        for i in range(self.audio_bitrate_combo.count()):
            if audio_bitrate_text in self.audio_bitrate_combo.itemText(i):
                self.audio_bitrate_combo.setCurrentIndex(i)
                break

        # 采样率
        sample_rate_text = f"{audio_config.sample_rate} Hz"
        for i in range(self.sample_rate_combo.count()):
            if sample_rate_text in self.sample_rate_combo.itemText(i):
                self.sample_rate_combo.setCurrentIndex(i)
                break

        # 声道数
        channels_text = f"{audio_config.channels}"
        for i in range(self.channels_combo.count()):
            if channels_text in self.channels_combo.itemText(i):
                self.channels_combo.setCurrentIndex(i)
                break

        # 音频编码器
        audio_codec_text = audio_config.codec
        for i in range(self.audio_codec_combo.count()):
            if audio_codec_text in self.audio_codec_combo.itemText(i):
                self.audio_codec_combo.setCurrentIndex(i)
                break

        # 音频处理设置
        self.volume_spin.setValue(config.app_config.get('audio_volume', 100))
        self.fade_in_spin.setValue(config.app_config.get('fade_in_duration', 1))
        self.fade_out_spin.setValue(config.app_config.get('fade_out_duration', 2))
    
    def save_settings(self):
        """保存设置"""
        try:
            # 保存API密钥
            pexels_key = self.pexels_api_edit.text().strip()
            pixabay_key = self.pixabay_api_edit.text().strip()

            if pexels_key:
                config.update_api_key("pexels", pexels_key)
            if pixabay_key:
                config.update_api_key("pixabay", pixabay_key)

            # 保存视频设置
            resolution_text = self.resolution_combo.currentText()
            if "1920x1080" in resolution_text:
                config.video.width, config.video.height = 1920, 1080
            elif "1280x720" in resolution_text:
                config.video.width, config.video.height = 1280, 720
            elif "854x480" in resolution_text:
                config.video.width, config.video.height = 854, 480
            elif "640x360" in resolution_text:
                config.video.width, config.video.height = 640, 360

            config.video.fps = int(self.fps_combo.currentText())

            # 视频码率
            bitrate_text = self.video_bitrate_combo.currentText()
            if "5000k" in bitrate_text:
                config.video.bitrate = 5000
            elif "3000k" in bitrate_text:
                config.video.bitrate = 3000
            elif "1500k" in bitrate_text:
                config.video.bitrate = 1500
            elif "800k" in bitrate_text:
                config.video.bitrate = 800

            # 视频编码器
            codec_text = self.video_codec_combo.currentText()
            if "libx264" in codec_text:
                config.video.codec = "libx264"
            elif "libx265" in codec_text:
                config.video.codec = "libx265"
            elif "libvpx-vp9" in codec_text:
                config.video.codec = "libvpx-vp9"

            # 编码预设
            preset_text = self.preset_combo.currentText()
            if "fast" in preset_text:
                config.video.preset = "fast"
            elif "medium" in preset_text:
                config.video.preset = "medium"
            elif "slow" in preset_text:
                config.video.preset = "slow"
            elif "veryslow" in preset_text:
                config.video.preset = "veryslow"

            # 视频处理设置
            config.app_config['max_clip_duration'] = self.max_clip_duration_spin.value()
            config.app_config['min_clip_duration'] = self.min_clip_duration_spin.value()
            config.app_config['scale_mode'] = self.scale_mode_combo.currentIndex()

            # 保存音频设置
            audio_bitrate_text = self.audio_bitrate_combo.currentText()
            if "320k" in audio_bitrate_text:
                config.audio.bitrate = 320
            elif "256k" in audio_bitrate_text:
                config.audio.bitrate = 256
            elif "192k" in audio_bitrate_text:
                config.audio.bitrate = 192
            elif "128k" in audio_bitrate_text:
                config.audio.bitrate = 128
            elif "96k" in audio_bitrate_text:
                config.audio.bitrate = 96

            # 采样率
            sample_rate_text = self.sample_rate_combo.currentText()
            config.audio.sample_rate = int(sample_rate_text.split()[0])

            # 声道数
            channels_text = self.channels_combo.currentText()
            config.audio.channels = int(channels_text.split()[0])

            # 音频编码器
            audio_codec_text = self.audio_codec_combo.currentText()
            config.audio.codec = audio_codec_text.split()[0]

            # 音频处理设置
            config.app_config['audio_volume'] = self.volume_spin.value()
            config.app_config['fade_in_duration'] = self.fade_in_spin.value()
            config.app_config['fade_out_duration'] = self.fade_out_spin.value()

            # 保存配置到文件
            config.save_config()

            QMessageBox.information(self, "成功", "设置已保存")
            self.accept()

        except Exception as e:
            self.logger.error(f"保存设置失败: {e}")
            QMessageBox.critical(self, "错误", f"保存设置失败：{e}")
    
    def show_api_help(self):
        """显示API帮助"""
        help_text = """
API密钥获取方法：

Pexels API:
1. 访问 https://www.pexels.com/api/
2. 注册账号并登录
3. 创建应用程序获取API密钥
4. 免费版本每月200次请求

Pixabay API:
1. 访问 https://pixabay.com/api/docs/
2. 注册账号并登录
3. 在账号设置中获取API密钥
4. 免费版本每分钟5次请求
        """
        
        QMessageBox.information(self, "API帮助", help_text)
