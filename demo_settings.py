#!/usr/bin/env python3
"""
设置功能演示脚本
展示新增的视频和音频参数设置功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def demo_video_settings():
    """演示视频设置功能"""
    print("📹 视频设置功能演示")
    print("-" * 30)
    
    from src.config.settings import config
    
    print("当前视频设置:")
    print(f"  分辨率: {config.video.width}x{config.video.height}")
    print(f"  帧率: {config.video.fps} fps")
    print(f"  编码器: {config.video.codec}")
    print(f"  预设: {config.video.preset}")
    print(f"  码率: {config.video.bitrate}k")
    
    print("\n视频处理参数:")
    print(f"  最大片段长度: {config.app_config.get('max_clip_duration')}秒")
    print(f"  最小片段长度: {config.app_config.get('min_clip_duration')}秒")
    print(f"  缩放模式: {config.app_config.get('scale_mode')}")
    
    # 演示修改设置
    print("\n🔧 修改视频设置演示:")
    original_bitrate = config.video.bitrate
    config.video.bitrate = 5000
    print(f"  码率从 {original_bitrate}k 修改为 {config.video.bitrate}k")
    
    original_preset = config.video.preset
    config.video.preset = "slow"
    print(f"  预设从 '{original_preset}' 修改为 '{config.video.preset}'")
    
    # 恢复原设置
    config.video.bitrate = original_bitrate
    config.video.preset = original_preset
    print("  (已恢复原设置)")

def demo_audio_settings():
    """演示音频设置功能"""
    print("\n🎵 音频设置功能演示")
    print("-" * 30)
    
    from src.config.settings import config
    
    print("当前音频设置:")
    print(f"  编码器: {config.audio.codec}")
    print(f"  码率: {config.audio.bitrate}k")
    print(f"  采样率: {config.audio.sample_rate} Hz")
    print(f"  声道数: {config.audio.channels}")
    
    print("\n音频处理参数:")
    print(f"  音量: {config.app_config.get('audio_volume')}%")
    print(f"  淡入时间: {config.app_config.get('fade_in_duration')}秒")
    print(f"  淡出时间: {config.app_config.get('fade_out_duration')}秒")
    
    # 演示修改设置
    print("\n🔧 修改音频设置演示:")
    original_bitrate = config.audio.bitrate
    config.audio.bitrate = 320
    print(f"  码率从 {original_bitrate}k 修改为 {config.audio.bitrate}k")
    
    original_volume = config.app_config.get('audio_volume')
    config.app_config['audio_volume'] = 80
    print(f"  音量从 {original_volume}% 修改为 {config.app_config['audio_volume']}%")
    
    # 恢复原设置
    config.audio.bitrate = original_bitrate
    config.app_config['audio_volume'] = original_volume
    print("  (已恢复原设置)")

def demo_available_options():
    """演示可用选项"""
    print("\n⚙️ 可用设置选项")
    print("-" * 30)
    
    print("视频分辨率选项:")
    resolutions = [
        "1920x1080 (1080p)",
        "1280x720 (720p)", 
        "854x480 (480p)",
        "640x360 (360p)"
    ]
    for res in resolutions:
        print(f"  • {res}")
    
    print("\n视频编码器选项:")
    codecs = [
        "libx264 (H.264) - 兼容性最好",
        "libx265 (H.265/HEVC) - 压缩率更高",
        "libvpx-vp9 (VP9) - 开源编码器"
    ]
    for codec in codecs:
        print(f"  • {codec}")
    
    print("\n编码预设选项:")
    presets = [
        "fast - 快速编码，文件较大",
        "medium - 平衡速度和质量",
        "slow - 慢速编码，质量更好",
        "veryslow - 极慢编码，最高质量"
    ]
    for preset in presets:
        print(f"  • {preset}")
    
    print("\n音频码率选项:")
    audio_bitrates = [
        "320k - 最高质量",
        "256k - 高质量",
        "192k - 标准质量",
        "128k - 中等质量",
        "96k - 低质量"
    ]
    for bitrate in audio_bitrates:
        print(f"  • {bitrate}")

def demo_sources():
    """演示音频和视频源"""
    print("\n📂 音频和视频源")
    print("-" * 30)
    
    from src.audio.audio_manager import audio_manager
    from src.video.video_manager import video_manager
    
    print("可用音频源:")
    for source in audio_manager.downloaders.keys():
        print(f"  • {source}")
    
    print("\n可用视频源:")
    for source in video_manager.downloaders.keys():
        print(f"  • {source}")
    
    print("\n推荐使用顺序:")
    print("音频源:")
    print("  1. local - 本地文件 (最可靠)")
    print("  2. youtube - YouTube (需要网络)")
    print("  3. netease - 网易云音乐 (可能受版权限制)")
    
    print("\n视频源:")
    print("  1. local - 本地文件 (最可靠)")
    print("  2. pexels - Pexels API (需要API密钥)")

def demo_ui_features():
    """演示界面功能"""
    print("\n🖥️ 用户界面功能")
    print("-" * 30)
    
    print("主窗口功能:")
    print("  • 歌曲信息输入 (标题、艺术家、关键词)")
    print("  • 参数设置 (音频源、视频源、输出路径)")
    print("  • 实时进度显示")
    print("  • 详细日志输出")
    print("  • 一键开始/停止生成")
    
    print("\n设置对话框功能:")
    print("  • API密钥配置 (Pexels、Pixabay)")
    print("  • 视频质量设置 (分辨率、帧率、码率)")
    print("  • 音频质量设置 (码率、采样率、声道)")
    print("  • 视频处理参数 (片段长度、缩放模式)")
    print("  • 音频处理参数 (音量、淡入淡出)")
    
    print("\n操作流程:")
    print("  1. 设置 → API设置 → 配置API密钥")
    print("  2. 设置 → 视频设置 → 调整视频参数")
    print("  3. 设置 → 音频设置 → 调整音频参数")
    print("  4. 主界面输入歌曲信息")
    print("  5. 选择音频源和视频源")
    print("  6. 点击开始生成")

def main():
    """主函数"""
    print("🎬 AI音乐视频生成器 - 设置功能演示")
    print("=" * 50)
    
    try:
        demo_video_settings()
        demo_audio_settings()
        demo_available_options()
        demo_sources()
        demo_ui_features()
        
        print("\n" + "=" * 50)
        print("✅ 功能演示完成！")
        print("\n📝 总结:")
        print("1. ✅ 视频源下拉框显示正常 (local, pexels)")
        print("2. ✅ 设置对话框包含完整的视频和音频参数")
        print("3. ✅ 支持自定义码率、分辨率、编码器等参数")
        print("4. ✅ 所有参数都可以通过界面进行配置")
        
        print("\n🚀 使用建议:")
        print("• 首次使用：配置API密钥和基本参数")
        print("• 高质量输出：选择高码率和慢速预设")
        print("• 快速测试：选择低码率和快速预设")
        print("• 本地文件：优先使用local音频和视频源")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
