"""
视频管理模块
负责视频素材获取、视频合成和渲染，支持多种视频源和FFmpeg处理
包括从Pexels、Pixabay等平台获取视频素材，以及视频合成功能
"""

import os
import requests
import subprocess
import ffmpeg
from pathlib import Path
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed
import random

from src.logger.logger import get_logger
from src.config.settings import config


@dataclass
class VideoClip:
    """视频片段类"""
    file_path: str = ""
    duration: float = 0.0
    width: int = 0
    height: int = 0
    fps: float = 0.0
    start_time: float = 0.0
    end_time: float = 0.0


class VideoDownloader:
    """视频下载器基类"""
    
    def __init__(self):
        self.logger = get_logger("VideoDownloader")
    
    def search(self, keywords: List[str], count: int = 10) -> List[Dict[str, Any]]:
        """
        搜索视频
        
        Args:
            keywords: 搜索关键词列表
            count: 返回数量
            
        Returns:
            List[Dict]: 搜索结果
        """
        raise NotImplementedError
    
    def download(self, url: str, output_path: str) -> bool:
        """
        下载视频
        
        Args:
            url: 视频URL
            output_path: 输出路径
            
        Returns:
            bool: 下载是否成功
        """
        raise NotImplementedError


class MockVideoDownloader(VideoDownloader):
    """模拟视频下载器（用于测试）"""
    
    def search(self, keywords: List[str], count: int = 10) -> List[Dict[str, Any]]:
        """模拟搜索视频"""
        results = []
        
        for i in range(min(count, 5)):  # 最多返回5个模拟结果
            results.append({
                'id': f'mock_{i}',
                'title': f'测试视频 {i+1}',
                'url': f'http://example.com/video_{i}.mp4',
                'thumbnail': f'http://example.com/thumb_{i}.jpg',
                'duration': random.randint(30, 300),
                'width': 1920,
                'height': 1080,
                'tags': keywords
            })
        
        self.logger.info(f"模拟搜索返回{len(results)}个视频")
        return results
    
    def download(self, url: str, output_path: str) -> bool:
        """模拟下载视频"""
        try:
            # 创建一个空的视频文件用于测试
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # 使用FFmpeg创建一个测试视频
            duration = random.randint(30, 120)
            
            # 创建彩色测试视频
            (
                ffmpeg
                .input('color=c=blue:s=1920x1080:d={}'.format(duration), f='lavfi')
                .output(output_path, vcodec='libx264', pix_fmt='yuv420p', t=duration)
                .overwrite_output()
                .run(quiet=True)
            )
            
            self.logger.info(f"模拟视频下载成功: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"模拟视频下载失败: {e}")
            return False


class VideoProcessor:
    """视频处理器"""

    def __init__(self):
        self.logger = get_logger("VideoProcessor")

    def _get_ffmpeg_path(self) -> str:
        """获取FFmpeg可执行文件路径"""
        return config.get_ffmpeg_path()

    def _get_ffprobe_path(self) -> str:
        """获取FFprobe可执行文件路径"""
        ffmpeg_path = self._get_ffmpeg_path()
        if ffmpeg_path.endswith("ffmpeg.exe"):
            return ffmpeg_path.replace("ffmpeg.exe", "ffprobe.exe")
        elif ffmpeg_path.endswith("ffmpeg"):
            return ffmpeg_path.replace("ffmpeg", "ffprobe")
        else:
            return "ffprobe"
    
    def get_video_info(self, video_path: str) -> Optional[VideoClip]:
        """
        获取视频信息

        Args:
            video_path: 视频文件路径

        Returns:
            VideoClip: 视频信息
        """
        try:
            if not os.path.exists(video_path):
                return None

            # 使用ffprobe获取视频信息
            ffprobe_path = self._get_ffprobe_path()
            probe = ffmpeg.probe(video_path, cmd=ffprobe_path)
            video_stream = next(
                (stream for stream in probe['streams'] if stream['codec_type'] == 'video'),
                None
            )
            
            if not video_stream:
                return None
            
            clip = VideoClip(
                file_path=video_path,
                duration=float(video_stream.get('duration', 0)),
                width=int(video_stream.get('width', 0)),
                height=int(video_stream.get('height', 0)),
                fps=eval(video_stream.get('r_frame_rate', '0/1'))
            )
            
            return clip
            
        except Exception as e:
            self.logger.error(f"获取视频信息失败: {e}")
            return None
    
    def trim_video(self, input_path: str, output_path: str, 
                  start_time: float, duration: float) -> bool:
        """
        裁剪视频
        
        Args:
            input_path: 输入视频路径
            output_path: 输出视频路径
            start_time: 开始时间（秒）
            duration: 持续时间（秒）
            
        Returns:
            bool: 处理是否成功
        """
        try:
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)
            
            (
                ffmpeg
                .input(input_path, ss=start_time, t=duration)
                .output(output_path, vcodec='libx264', acodec='aac')
                .overwrite_output()
                .run(cmd=self._get_ffmpeg_path(), quiet=True)
            )
            
            self.logger.info(f"视频裁剪成功: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"视频裁剪失败: {e}")
            return False
    
    def resize_video(self, input_path: str, output_path: str, 
                    width: int, height: int) -> bool:
        """
        调整视频尺寸
        
        Args:
            input_path: 输入视频路径
            output_path: 输出视频路径
            width: 目标宽度
            height: 目标高度
            
        Returns:
            bool: 处理是否成功
        """
        try:
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)
            
            (
                ffmpeg
                .input(input_path)
                .filter('scale', width, height)
                .output(output_path, vcodec='libx264', acodec='aac')
                .overwrite_output()
                .run(cmd=self._get_ffmpeg_path(), quiet=True)
            )
            
            self.logger.info(f"视频尺寸调整成功: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"视频尺寸调整失败: {e}")
            return False
    
    def concatenate_videos(self, video_paths: List[str], output_path: str) -> bool:
        """
        拼接视频
        
        Args:
            video_paths: 视频文件路径列表
            output_path: 输出视频路径
            
        Returns:
            bool: 处理是否成功
        """
        try:
            if not video_paths:
                return False
            
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # 创建输入流
            inputs = [ffmpeg.input(path) for path in video_paths]
            
            # 拼接视频
            (
                ffmpeg
                .concat(*inputs, v=1, a=0)
                .output(output_path, vcodec='libx264')
                .overwrite_output()
                .run(cmd=self._get_ffmpeg_path(), quiet=True)
            )
            
            self.logger.info(f"视频拼接成功: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"视频拼接失败: {e}")
            return False
    
    def add_audio_to_video(self, video_path: str, audio_path: str, 
                          output_path: str) -> bool:
        """
        为视频添加音频
        
        Args:
            video_path: 视频文件路径
            audio_path: 音频文件路径
            output_path: 输出文件路径
            
        Returns:
            bool: 处理是否成功
        """
        try:
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)
            
            video_input = ffmpeg.input(video_path)
            audio_input = ffmpeg.input(audio_path)
            
            (
                ffmpeg
                .output(video_input, audio_input, output_path,
                       vcodec='libx264', acodec='aac', shortest=None)
                .overwrite_output()
                .run(cmd=self._get_ffmpeg_path(), quiet=True)
            )
            
            self.logger.info(f"视频添加音频成功: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"视频添加音频失败: {e}")
            return False
    
    def add_subtitle_to_video(self, video_path: str, subtitle_path: str, 
                             output_path: str) -> bool:
        """
        为视频添加字幕
        
        Args:
            video_path: 视频文件路径
            subtitle_path: 字幕文件路径
            output_path: 输出文件路径
            
        Returns:
            bool: 处理是否成功
        """
        try:
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # 根据字幕格式选择滤镜
            if subtitle_path.lower().endswith('.srt'):
                subtitle_filter = f"subtitles={subtitle_path}"
            elif subtitle_path.lower().endswith('.ass'):
                subtitle_filter = f"ass={subtitle_path}"
            else:
                self.logger.error(f"不支持的字幕格式: {subtitle_path}")
                return False
            
            (
                ffmpeg
                .input(video_path)
                .filter('subtitles', subtitle_path)
                .output(output_path, vcodec='libx264', acodec='copy')
                .overwrite_output()
                .run(cmd=self._get_ffmpeg_path(), quiet=True)
            )
            
            self.logger.info(f"视频添加字幕成功: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"视频添加字幕失败: {e}")
            return False


class VideoManager:
    """视频管理器"""
    
    def __init__(self):
        self.logger = get_logger("VideoManager")
        self.downloaders = {
            'mock': MockVideoDownloader()
        }
        self.processor = VideoProcessor()
        self.temp_dir = config.get_temp_dir() / "video"
        self.temp_dir.mkdir(parents=True, exist_ok=True)
    
    def search_videos(self, keywords: List[str], count: int = 10, 
                     source: str = "mock") -> List[Dict[str, Any]]:
        """
        搜索视频
        
        Args:
            keywords: 搜索关键词
            count: 返回数量
            source: 视频源
            
        Returns:
            List[Dict]: 搜索结果
        """
        if source not in self.downloaders:
            self.logger.error(f"不支持的视频源: {source}")
            return []
        
        self.logger.info(f"搜索视频: {keywords} (来源: {source})")
        return self.downloaders[source].search(keywords, count)
    
    def download_videos(self, video_urls: List[str], 
                       source: str = "mock") -> List[str]:
        """
        批量下载视频
        
        Args:
            video_urls: 视频URL列表
            source: 视频源
            
        Returns:
            List[str]: 下载成功的文件路径列表
        """
        if source not in self.downloaders:
            self.logger.error(f"不支持的视频源: {source}")
            return []
        
        downloaded_files = []
        downloader = self.downloaders[source]
        
        # 使用线程池并行下载
        with ThreadPoolExecutor(max_workers=config.app_config['max_concurrent_downloads']) as executor:
            future_to_url = {}
            
            for i, url in enumerate(video_urls):
                filename = f"video_{i}_{hash(url) % 10000}.mp4"
                output_path = self.temp_dir / filename
                
                future = executor.submit(downloader.download, url, str(output_path))
                future_to_url[future] = str(output_path)
            
            for future in as_completed(future_to_url):
                output_path = future_to_url[future]
                try:
                    success = future.result()
                    if success:
                        downloaded_files.append(output_path)
                except Exception as e:
                    self.logger.error(f"下载视频失败: {e}")
        
        self.logger.info(f"成功下载{len(downloaded_files)}个视频文件")
        return downloaded_files
    
    def create_music_video(self, video_files: List[str], audio_file: str,
                          subtitle_file: str, output_path: str,
                          target_duration: float) -> bool:
        """
        创建音乐视频
        
        Args:
            video_files: 视频文件列表
            audio_file: 音频文件路径
            subtitle_file: 字幕文件路径
            output_path: 输出文件路径
            target_duration: 目标时长
            
        Returns:
            bool: 创建是否成功
        """
        try:
            if not video_files or not os.path.exists(audio_file):
                self.logger.error("视频文件或音频文件不存在")
                return False
            
            # 1. 处理视频片段
            processed_clips = []
            current_duration = 0.0
            
            for video_file in video_files:
                if current_duration >= target_duration:
                    break
                
                clip_info = self.processor.get_video_info(video_file)
                if not clip_info:
                    continue
                
                # 计算需要的片段长度
                remaining_duration = target_duration - current_duration
                clip_duration = min(clip_info.duration, remaining_duration)
                
                # 随机选择开始时间
                max_start = max(0, clip_info.duration - clip_duration)
                start_time = random.uniform(0, max_start) if max_start > 0 else 0
                
                # 裁剪视频片段
                clip_filename = f"clip_{len(processed_clips)}.mp4"
                clip_path = self.temp_dir / clip_filename
                
                if self.processor.trim_video(video_file, str(clip_path), 
                                           start_time, clip_duration):
                    processed_clips.append(str(clip_path))
                    current_duration += clip_duration
            
            if not processed_clips:
                self.logger.error("没有可用的视频片段")
                return False
            
            # 2. 拼接视频片段
            concatenated_video = self.temp_dir / "concatenated.mp4"
            if not self.processor.concatenate_videos(processed_clips, 
                                                   str(concatenated_video)):
                return False
            
            # 3. 添加音频
            video_with_audio = self.temp_dir / "with_audio.mp4"
            if not self.processor.add_audio_to_video(str(concatenated_video),
                                                   audio_file, 
                                                   str(video_with_audio)):
                return False
            
            # 4. 添加字幕
            if subtitle_file and os.path.exists(subtitle_file):
                if not self.processor.add_subtitle_to_video(str(video_with_audio),
                                                          subtitle_file,
                                                          output_path):
                    return False
            else:
                # 如果没有字幕，直接复制文件
                import shutil
                shutil.copy2(str(video_with_audio), output_path)
            
            self.logger.info(f"音乐视频创建成功: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"创建音乐视频失败: {e}")
            return False
    
    def cleanup_temp_files(self) -> None:
        """清理临时文件"""
        try:
            for file_path in self.temp_dir.glob("*"):
                if file_path.is_file():
                    file_path.unlink()
            self.logger.info("视频临时文件清理完成")
        except Exception as e:
            self.logger.error(f"清理视频临时文件失败: {e}")


# 全局视频管理器实例
video_manager = VideoManager()
