"""
视频管理模块
负责视频素材获取、视频合成和渲染，支持多种视频源和FFmpeg处理
包括从Pexels、Pixabay等平台获取视频素材，以及视频合成功能
"""

import os
import requests
import subprocess
import ffmpeg
from pathlib import Path
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed
import random

from src.logger.logger import get_logger
from src.config.settings import config


@dataclass
class VideoClip:
    """视频片段类"""
    file_path: str = ""
    duration: float = 0.0
    width: int = 0
    height: int = 0
    fps: float = 0.0
    start_time: float = 0.0
    end_time: float = 0.0


class VideoDownloader:
    """视频下载器基类"""
    
    def __init__(self):
        self.logger = get_logger("VideoDownloader")
    
    def search(self, keywords: List[str], count: int = 10) -> List[Dict[str, Any]]:
        """
        搜索视频
        
        Args:
            keywords: 搜索关键词列表
            count: 返回数量
            
        Returns:
            List[Dict]: 搜索结果
        """
        raise NotImplementedError
    
    def download(self, url: str, output_path: str) -> bool:
        """
        下载视频
        
        Args:
            url: 视频URL
            output_path: 输出路径
            
        Returns:
            bool: 下载是否成功
        """
        raise NotImplementedError


class PexelsVideoDownloader(VideoDownloader):
    """Pexels视频下载器"""

    def __init__(self):
        super().__init__()
        self.api_key = config.get_api_key("pexels")
        self.base_url = "https://api.pexels.com/videos"
        self.headers = {
            "Authorization": self.api_key
        } if self.api_key else {}

    def search(self, keywords: List[str], count: int = 10) -> List[Dict[str, Any]]:
        """搜索Pexels视频"""
        if not self.api_key:
            self.logger.error("Pexels API密钥未设置")
            return []

        try:
            query = " ".join(keywords)
            url = f"{self.base_url}/search"
            params = {
                "query": query,
                "per_page": min(count, 80),  # Pexels最大80
                "orientation": "landscape"
            }

            response = requests.get(url, headers=self.headers, params=params)
            response.raise_for_status()

            data = response.json()
            videos = []

            if 'videos' in data:
                for video in data['videos']:
                    # 选择最高质量的视频文件
                    video_files = video.get('video_files', [])
                    if not video_files:
                        continue

                    # 按质量排序，选择最佳质量
                    best_video = max(video_files, key=lambda x: x.get('width', 0) * x.get('height', 0))

                    videos.append({
                        'id': video.get('id'),
                        'title': f"Pexels Video {video.get('id')}",
                        'url': best_video.get('link'),
                        'thumbnail': video.get('image'),
                        'duration': video.get('duration', 30),
                        'width': best_video.get('width', 1920),
                        'height': best_video.get('height', 1080),
                        'tags': keywords
                    })

            self.logger.info(f"Pexels搜索到{len(videos)}个视频")
            return videos

        except Exception as e:
            self.logger.error(f"Pexels视频搜索失败: {e}")
            return []

    def download(self, url: str, output_path: str) -> bool:
        """下载Pexels视频"""
        try:
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)

            # 下载视频文件
            response = requests.get(url, stream=True)
            response.raise_for_status()

            with open(output_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            self.logger.info(f"Pexels视频下载成功: {output_path}")
            return True

        except Exception as e:
            self.logger.error(f"Pexels视频下载失败: {e}")
            return False


class VideoProcessor:
    """优化的视频处理器"""

    def __init__(self):
        self.logger = get_logger("VideoProcessor")
        self._video_info_cache = {}  # 视频信息缓存

    def _get_ffmpeg_path(self) -> str:
        """获取FFmpeg可执行文件路径"""
        return config.get_ffmpeg_path()

    def _get_ffprobe_path(self) -> str:
        """获取FFprobe可执行文件路径"""
        ffmpeg_path = self._get_ffmpeg_path()
        if ffmpeg_path.endswith("ffmpeg.exe"):
            return ffmpeg_path.replace("ffmpeg.exe", "ffprobe.exe")
        elif ffmpeg_path.endswith("ffmpeg"):
            return ffmpeg_path.replace("ffmpeg", "ffprobe")
        else:
            return "ffprobe"

    def _handle_ffmpeg_error(self, error: ffmpeg.Error, operation: str) -> str:
        """统一的FFmpeg错误处理"""
        try:
            if hasattr(error, 'stderr') and error.stderr:
                error_msg = error.stderr.decode('utf-8', errors='ignore')
            else:
                error_msg = str(error)
        except:
            error_msg = str(error)

        self.logger.error(f"{operation}失败: {error_msg}")
        return error_msg

    def _run_ffmpeg_command(self, stream, operation: str, timeout: int = 120) -> bool:
        """统一的FFmpeg命令执行"""
        try:
            stream.run(cmd=self._get_ffmpeg_path(),
                      quiet=True,
                      capture_stdout=True,
                      capture_stderr=True,
                      timeout=timeout)
            return True
        except ffmpeg.Error as e:
            self._handle_ffmpeg_error(e, operation)
            return False
        except Exception as e:
            self.logger.error(f"{operation}异常: {e}")
            return False
    
    def get_video_info(self, video_path: str) -> Optional[VideoClip]:
        """
        获取视频信息（带缓存优化）

        Args:
            video_path: 视频文件路径

        Returns:
            VideoClip: 视频信息
        """
        try:
            if not os.path.exists(video_path):
                return None

            # 检查缓存
            file_stat = os.stat(video_path)
            cache_key = f"{video_path}_{file_stat.st_mtime}_{file_stat.st_size}"

            if cache_key in self._video_info_cache:
                return self._video_info_cache[cache_key]

            # 使用ffprobe获取视频信息
            ffprobe_path = self._get_ffprobe_path()
            probe = ffmpeg.probe(video_path, cmd=ffprobe_path)
            video_stream = next(
                (stream for stream in probe['streams'] if stream['codec_type'] == 'video'),
                None
            )

            if not video_stream:
                return None

            # 安全的帧率计算
            fps = 30.0  # 默认帧率
            try:
                r_frame_rate = video_stream.get('r_frame_rate', '30/1')
                if '/' in r_frame_rate:
                    num, den = r_frame_rate.split('/')
                    fps = float(num) / float(den) if float(den) != 0 else 30.0
                else:
                    fps = float(r_frame_rate)
            except:
                fps = 30.0

            clip = VideoClip(
                file_path=video_path,
                duration=float(video_stream.get('duration', 0)),
                width=int(video_stream.get('width', 1920)),
                height=int(video_stream.get('height', 1080)),
                fps=fps
            )

            # 缓存结果
            self._video_info_cache[cache_key] = clip

            return clip

        except Exception as e:
            self.logger.error(f"获取视频信息失败: {e}")
            return None
    
    def trim_video(self, input_path: str, output_path: str, 
                  start_time: float, duration: float) -> bool:
        """
        裁剪视频
        
        Args:
            input_path: 输入视频路径
            output_path: 输出视频路径
            start_time: 开始时间（秒）
            duration: 持续时间（秒）
            
        Returns:
            bool: 处理是否成功
        """
        try:
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)
            
            (
                ffmpeg
                .input(input_path, ss=start_time, t=duration)
                .output(output_path, vcodec='libx264', acodec='aac')
                .overwrite_output()
                .run(cmd=self._get_ffmpeg_path(), quiet=True)
            )
            
            self.logger.info(f"视频裁剪成功: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"视频裁剪失败: {e}")
            return False
    
    def resize_video(self, input_path: str, output_path: str, 
                    width: int, height: int) -> bool:
        """
        调整视频尺寸
        
        Args:
            input_path: 输入视频路径
            output_path: 输出视频路径
            width: 目标宽度
            height: 目标高度
            
        Returns:
            bool: 处理是否成功
        """
        try:
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)
            
            (
                ffmpeg
                .input(input_path)
                .filter('scale', width, height)
                .output(output_path, vcodec='libx264', acodec='aac')
                .overwrite_output()
                .run(cmd=self._get_ffmpeg_path(), quiet=True)
            )
            
            self.logger.info(f"视频尺寸调整成功: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"视频尺寸调整失败: {e}")
            return False
    
    def concatenate_videos(self, video_paths: List[str], output_path: str) -> bool:
        """
        拼接视频（优化版本）

        Args:
            video_paths: 视频文件路径列表
            output_path: 输出视频路径

        Returns:
            bool: 处理是否成功
        """
        try:
            if not video_paths:
                self.logger.error("视频文件列表为空")
                return False

            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)

            self.logger.info(f"开始拼接{len(video_paths)}个视频文件")

            # 如果只有一个视频文件，直接复制
            if len(video_paths) == 1:
                import shutil
                shutil.copy2(video_paths[0], output_path)
                self.logger.info(f"单个视频文件复制成功: {output_path}")
                return True

            # 使用优化的filter_complex方法
            return self._concatenate_with_filter_complex(video_paths, output_path)

        except Exception as e:
            self.logger.error(f"视频拼接失败: {e}")
            return False

    def _concatenate_with_filter_complex(self, video_paths: List[str], output_path: str) -> bool:
        """使用filter_complex拼接视频（优化版本）"""
        try:
            # 获取视频配置
            video_config = config.video

            # 创建输入流
            inputs = [ffmpeg.input(path) for path in video_paths]

            # 构建输出参数
            output_params = {
                'vcodec': video_config.codec,
                'acodec': 'aac',
                'preset': video_config.preset,
                **{'b:v': f'{video_config.bitrate}k'}
            }

            # 使用filter_complex进行拼接
            stream = (
                ffmpeg
                .concat(*inputs, v=1, a=0, unsafe=1)
                .output(output_path, **output_params)
                .overwrite_output()
            )

            # 执行命令
            if self._run_ffmpeg_command(stream, "视频拼接"):
                self.logger.info(f"filter_complex拼接成功: {output_path}")
                return True

            return False

        except Exception as e:
            self.logger.error(f"filter_complex拼接异常: {e}")
            return False


    
    def add_audio_to_video(self, video_path: str, audio_path: str,
                          output_path: str) -> bool:
        """
        为视频添加音频（优化版本）

        Args:
            video_path: 视频文件路径
            audio_path: 音频文件路径
            output_path: 输出文件路径

        Returns:
            bool: 处理是否成功
        """
        try:
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)

            # 获取配置
            audio_config = config.audio
            video_config = config.video

            # 构建FFmpeg流
            video_input = ffmpeg.input(video_path)
            audio_input = ffmpeg.input(audio_path)

            # 应用音频滤镜
            audio_input = self._apply_audio_filters(audio_input)

            # 构建输出参数
            output_params = {
                'acodec': audio_config.codec,
                'ar': audio_config.sample_rate,
                'ac': audio_config.channels,
                **{'b:a': f'{audio_config.bitrate}k'}
            }

            # 尝试不重新编码视频（更快）
            output_params['vcodec'] = 'copy'

            stream = ffmpeg.output(
                video_input['v'], audio_input['a'],
                output_path,
                **output_params
            ).overwrite_output()

            # 执行命令
            if self._run_ffmpeg_command(stream, "音频添加"):
                self.logger.info(f"视频添加音频成功: {output_path}")
                return True

            # 如果失败，尝试重新编码视频
            self.logger.info("尝试重新编码视频...")
            output_params.update({
                'vcodec': video_config.codec,
                'preset': video_config.preset,
                **{'b:v': f'{video_config.bitrate}k'}
            })

            stream = ffmpeg.output(
                video_input['v'], audio_input['a'],
                output_path,
                **output_params
            ).overwrite_output()

            if self._run_ffmpeg_command(stream, "音频添加(重新编码)", timeout=180):
                self.logger.info(f"视频添加音频成功(重新编码): {output_path}")
                return True

            return False

        except Exception as e:
            self.logger.error(f"视频添加音频异常: {e}")
            return False

    def _apply_audio_filters(self, audio_input):
        """应用音频滤镜"""
        volume = config.app_config.get('audio_volume', 100)
        fade_in = config.app_config.get('fade_in_duration', 1)
        fade_out = config.app_config.get('fade_out_duration', 2)

        # 音量调节
        if volume != 100:
            audio_input = audio_input.filter('volume', volume/100)

        # 淡入效果
        if fade_in > 0:
            audio_input = audio_input.filter('afade', t='in', ss=0, d=fade_in)

        # 淡出效果
        if fade_out > 0:
            audio_input = audio_input.filter('afade', t='out', d=fade_out)

        return audio_input


    
    def add_subtitle_to_video(self, video_path: str, subtitle_path: str, 
                             output_path: str) -> bool:
        """
        为视频添加字幕
        
        Args:
            video_path: 视频文件路径
            subtitle_path: 字幕文件路径
            output_path: 输出文件路径
            
        Returns:
            bool: 处理是否成功
        """
        try:
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)

            # 获取字幕配置
            subtitle_config = config.subtitle

            # 方法1：使用subtitles滤镜（推荐）
            try:
                # 构建字幕样式参数
                style_params = self._build_subtitle_style(subtitle_config)

                # 确保音频流被正确处理
                video_input = ffmpeg.input(video_path)
                video_with_subs = video_input.video.filter('subtitles', subtitle_path, **style_params)
                audio_stream = video_input.audio

                (
                    ffmpeg
                    .output(video_with_subs, audio_stream, output_path,
                           vcodec=config.video.codec,
                           acodec='copy',
                           preset=config.video.preset)
                    .overwrite_output()
                    .run(cmd=self._get_ffmpeg_path(), quiet=False, capture_stdout=True, capture_stderr=True)
                )

                self.logger.info(f"视频添加字幕成功: {output_path}")
                return True

            except ffmpeg.Error as e:
                try:
                    error_msg = e.stderr.decode('utf-8', errors='ignore') if e.stderr else str(e)
                except:
                    error_msg = str(e)
                self.logger.warning(f"subtitles滤镜失败，尝试备用方案: {error_msg}")

                # 方法2：使用drawtext滤镜（备用）
                return self._add_subtitle_with_drawtext(video_path, subtitle_path, output_path)
            
        except Exception as e:
            self.logger.error(f"视频添加字幕失败: {e}")
            return False

    def _build_subtitle_style(self, subtitle_config) -> dict:
        """构建字幕样式参数"""
        style_params = {}

        # 构建force_style字符串
        force_style_parts = []

        # 字体设置
        if hasattr(subtitle_config, 'font_family') and subtitle_config.font_family:
            force_style_parts.append(f"FontName={subtitle_config.font_family}")

        # 字体大小
        if hasattr(subtitle_config, 'font_size') and subtitle_config.font_size:
            force_style_parts.append(f"FontSize={subtitle_config.font_size}")

        # 字体颜色 (转换为ASS格式)
        if hasattr(subtitle_config, 'font_color') and subtitle_config.font_color:
            # 将#FFFFFF格式转换为&HFFFFFF格式
            color = subtitle_config.font_color.replace('#', '&H')
            if len(color) == 8:  # &HFFFFFF
                # ASS格式是BGR，需要转换RGB到BGR
                r = color[2:4]
                g = color[4:6]
                b = color[6:8]
                bgr_color = f"&H{b}{g}{r}"
                force_style_parts.append(f"PrimaryColour={bgr_color}")

        # 描边颜色
        if hasattr(subtitle_config, 'outline_color') and subtitle_config.outline_color:
            color = subtitle_config.outline_color.replace('#', '&H')
            if len(color) == 8:
                r = color[2:4]
                g = color[4:6]
                b = color[6:8]
                bgr_color = f"&H{b}{g}{r}"
                force_style_parts.append(f"OutlineColour={bgr_color}")

        # 描边宽度
        if hasattr(subtitle_config, 'outline_width'):
            force_style_parts.append(f"Outline={subtitle_config.outline_width}")

        # 阴影
        if hasattr(subtitle_config, 'shadow'):
            force_style_parts.append(f"Shadow={subtitle_config.shadow}")

        # 对齐方式
        if hasattr(subtitle_config, 'alignment'):
            force_style_parts.append(f"Alignment={subtitle_config.alignment}")

        # 边距
        if hasattr(subtitle_config, 'margin_v'):
            force_style_parts.append(f"MarginV={subtitle_config.margin_v}")

        if force_style_parts:
            style_params['force_style'] = ','.join(force_style_parts)

        return style_params

    def _add_subtitle_with_drawtext(self, video_path: str, subtitle_path: str, output_path: str) -> bool:
        """使用drawtext滤镜添加字幕（备用方案）"""
        try:
            # 读取字幕文件内容
            subtitle_lines = self._parse_subtitle_file(subtitle_path)

            if not subtitle_lines:
                self.logger.warning("字幕文件为空或解析失败")
                return False

            # 获取字幕配置
            subtitle_config = config.subtitle

            # 构建drawtext滤镜链
            video_input = ffmpeg.input(video_path)

            # 为每行字幕创建drawtext滤镜
            for i, (start_time, end_time, text) in enumerate(subtitle_lines[:20]):  # 限制前20行
                # 清理文本，移除特殊字符
                clean_text = text.replace("'", "").replace('"', '').replace('\\', '').replace(':', '').replace('\n', ' ')

                if not clean_text.strip():
                    continue

                # 计算位置
                if subtitle_config.position == "top":
                    y_pos = "50"
                elif subtitle_config.position == "center":
                    y_pos = "(h-text_h)/2"
                else:  # bottom
                    y_pos = f"h-text_h-{getattr(subtitle_config, 'margin_v', 50)}"

                video_input = video_input.filter(
                    'drawtext',
                    text=clean_text,
                    fontsize=subtitle_config.font_size,
                    fontcolor=subtitle_config.font_color,
                    x='(w-text_w)/2',  # 居中
                    y=y_pos,
                    enable=f'between(t,{start_time},{end_time})',
                    borderw=getattr(subtitle_config, 'outline_width', 2),
                    bordercolor=getattr(subtitle_config, 'outline_color', '#000000')
                )

            (
                video_input
                .output(output_path, vcodec=config.video.codec, acodec='copy')
                .overwrite_output()
                .run(cmd=self._get_ffmpeg_path(), quiet=True)
            )

            self.logger.info(f"使用drawtext添加字幕成功: {output_path}")
            return True

        except Exception as e:
            self.logger.error(f"drawtext字幕添加失败: {e}")
            return False

    def _parse_subtitle_file(self, subtitle_path: str) -> list:
        """解析字幕文件"""
        try:
            import re

            with open(subtitle_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 解析SRT格式
            pattern = r'(\d+)\n(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})\n(.*?)(?=\n\d+\n|\n*$)'
            matches = re.findall(pattern, content, re.DOTALL)

            subtitle_lines = []
            for match in matches:
                start_time = self._time_to_seconds(match[1])
                end_time = self._time_to_seconds(match[2])
                text = match[3].strip()
                subtitle_lines.append((start_time, end_time, text))

            return subtitle_lines

        except Exception as e:
            self.logger.error(f"解析字幕文件失败: {e}")
            return []

    def _time_to_seconds(self, time_str: str) -> float:
        """将时间字符串转换为秒数"""
        try:
            # 格式: HH:MM:SS,mmm
            time_part, ms_part = time_str.split(',')
            h, m, s = map(int, time_part.split(':'))
            ms = int(ms_part)
            return h * 3600 + m * 60 + s + ms / 1000.0
        except:
            return 0.0

    def extract_frames(self, video_path: str, output_dir: str,
                      interval: float = 5.0, count: int = 3,
                      quality: str = "high") -> List[str]:
        """
        从视频中抽取帧

        Args:
            video_path: 视频文件路径
            output_dir: 输出目录
            interval: 抽帧间隔(秒)
            count: 每次抽帧数量
            quality: 抽帧质量 (low, medium, high)

        Returns:
            List[str]: 抽取的帧文件路径列表
        """
        try:
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)

            # 获取视频信息
            video_info = self.get_video_info(video_path)
            if not video_info:
                self.logger.error(f"无法获取视频信息: {video_path}")
                return []

            # 计算抽帧时间点
            duration = video_info.duration
            frame_times = []

            current_time = 0
            while current_time < duration and len(frame_times) < count:
                frame_times.append(current_time)
                current_time += interval

            if not frame_times:
                self.logger.warning("没有可抽取的帧")
                return []

            # 设置质量参数
            quality_settings = {
                "low": {"scale": "640:360", "quality": 5},
                "medium": {"scale": "1280:720", "quality": 3},
                "high": {"scale": "1920:1080", "quality": 1}
            }

            settings = quality_settings.get(quality, quality_settings["high"])

            # 抽取帧
            extracted_frames = []
            video_name = Path(video_path).stem

            for i, time_point in enumerate(frame_times):
                frame_filename = f"{video_name}_frame_{i:03d}_{time_point:.1f}s.jpg"
                frame_path = output_path / frame_filename

                try:
                    # 使用ffmpeg-python抽帧
                    stream = (
                        ffmpeg
                        .input(video_path)
                        .output(str(frame_path),
                               ss=time_point,
                               vframes=1,
                               vf=f"scale={settings['scale']}",
                               **{'q:v': settings["quality"]})
                        .overwrite_output()
                    )

                    success = self._run_ffmpeg_command(stream, f"抽帧{frame_filename}", timeout=30)

                    if success and frame_path.exists() and frame_path.stat().st_size > 0:
                        extracted_frames.append(str(frame_path))
                        self.logger.debug(f"抽帧成功: {frame_filename} (时间: {time_point:.1f}s, 大小: {frame_path.stat().st_size} 字节)")
                    else:
                        self.logger.warning(f"抽帧失败或文件无效: {frame_filename}")

                except Exception as e:
                    self.logger.warning(f"抽帧异常 {frame_filename}: {e}")
                    continue

            self.logger.info(f"视频抽帧完成: {len(extracted_frames)}/{len(frame_times)} 帧")
            return extracted_frames

        except Exception as e:
            self.logger.error(f"视频抽帧失败: {e}")
            return []



    def add_watermark_to_video(self, video_path: str, output_path: str) -> bool:
        """
        为视频添加水印

        Args:
            video_path: 视频文件路径
            output_path: 输出文件路径

        Returns:
            bool: 处理是否成功
        """
        try:
            watermark_config = config.watermark

            if not watermark_config.enabled:
                # 如果水印未启用，直接复制文件
                import shutil
                shutil.copy2(video_path, output_path)
                return True

            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)

            # 获取视频信息用于计算位置
            video_info = self.get_video_info(video_path)
            if not video_info:
                self.logger.error("无法获取视频信息")
                return False

            # 构建水印滤镜
            watermark_filter = self._build_watermark_filter(watermark_config, video_info)

            # 确保音频流被正确处理
            video_input = ffmpeg.input(video_path)
            video_with_watermark = video_input.video.filter('drawtext', **watermark_filter)
            audio_stream = video_input.audio

            # 构建输出流
            stream = ffmpeg.output(
                video_with_watermark, audio_stream, output_path,
                vcodec=config.video.codec,
                acodec='copy',
                preset=config.video.preset
            ).overwrite_output()

            # 执行命令
            if self._run_ffmpeg_command(stream, "水印添加"):
                self.logger.info(f"视频添加水印成功: {output_path}")
                return True

            return False

        except Exception as e:
            self.logger.error(f"视频添加水印异常: {e}")
            return False

    def _build_watermark_filter(self, watermark_config, video_info) -> dict:
        """构建水印滤镜参数"""
        filter_params = {
            'text': watermark_config.text,
            'fontsize': watermark_config.font_size,
            'fontcolor': f"{watermark_config.font_color}@{watermark_config.opacity}",
            'borderw': watermark_config.outline_width,
            'bordercolor': watermark_config.outline_color
        }

        # 计算位置
        x_pos, y_pos = self._calculate_watermark_position(
            watermark_config, video_info.width, video_info.height
        )

        # 如果启用移动效果
        if watermark_config.movement_enabled:
            x_expr, y_expr = self._build_movement_expressions(
                watermark_config, x_pos, y_pos, video_info.width, video_info.height
            )
            filter_params['x'] = x_expr
            filter_params['y'] = y_expr
        else:
            filter_params['x'] = str(x_pos)
            filter_params['y'] = str(y_pos)

        return filter_params

    def _calculate_watermark_position(self, watermark_config, width: int, height: int) -> tuple:
        """计算水印位置"""
        margin_x = watermark_config.margin_x
        margin_y = watermark_config.margin_y

        position_map = {
            'top_left': (margin_x, margin_y),
            'top_right': (width - margin_x, margin_y),
            'bottom_left': (margin_x, height - margin_y),
            'bottom_right': (width - margin_x, height - margin_y),
            'center': (width // 2, height // 2)
        }

        return position_map.get(watermark_config.position, position_map['bottom_right'])

    def _build_movement_expressions(self, watermark_config, base_x: int, base_y: int,
                                  width: int, height: int) -> tuple:
        """构建移动表达式"""
        speed = watermark_config.movement_speed
        range_val = watermark_config.movement_range

        if watermark_config.movement_type == "linear":
            # 水平直线移动
            x_expr = f"({base_x} + {range_val} * sin({speed} * t))"
            y_expr = str(base_y)

        elif watermark_config.movement_type == "circular":
            # 圆形移动
            radius = range_val // 2
            x_expr = f"({base_x} + {radius} * cos({speed} * t))"
            y_expr = f"({base_y} + {radius} * sin({speed} * t))"

        elif watermark_config.movement_type == "bounce":
            # 弹跳移动
            x_expr = f"({base_x} + {range_val} * abs(sin({speed} * t)))"
            y_expr = f"({base_y} + {range_val//2} * abs(cos({speed} * t)))"

        elif watermark_config.movement_type == "random":
            # 伪随机移动（使用多个正弦波组合）
            x_expr = f"({base_x} + {range_val//2} * (sin({speed} * t) + 0.5 * sin({speed * 2.3} * t)))"
            y_expr = f"({base_y} + {range_val//2} * (cos({speed * 1.7} * t) + 0.3 * cos({speed * 3.1} * t)))"

        else:
            # 默认静态
            x_expr = str(base_x)
            y_expr = str(base_y)

        # 确保水印不会移出屏幕
        x_expr = f"max(0, min({width} - text_w, {x_expr}))"
        y_expr = f"max(0, min({height} - text_h, {y_expr}))"

        return x_expr, y_expr


class PixabayVideoDownloader(VideoDownloader):
    """Pixabay视频下载器"""

    def __init__(self):
        super().__init__()
        self.api_key = config.get_api_key("pixabay")
        self.base_url = "https://pixabay.com/api/videos/"

    def search(self, keywords: List[str], count: int = 10) -> List[Dict[str, Any]]:
        """搜索Pixabay视频"""
        if not self.api_key:
            self.logger.error("Pixabay API密钥未配置")
            return []

        try:
            import requests

            # 构建搜索参数
            params = {
                'key': self.api_key,
                'q': ' '.join(keywords),
                'video_type': 'all',
                'category': 'music',
                'per_page': min(count, 20),
                'safesearch': 'true',
                'order': 'popular'
            }

            response = requests.get(self.base_url, params=params, timeout=10)
            response.raise_for_status()

            data = response.json()
            videos = []

            for video in data.get('hits', []):
                # 选择合适的视频质量
                video_url = self._get_best_video_url(video.get('videos', {}))

                if video_url:
                    videos.append({
                        'id': str(video.get('id')),
                        'title': video.get('tags', '').replace(',', ' '),
                        'url': video_url,
                        'thumbnail': video.get('webformatURL', ''),
                        'duration': video.get('duration', 30),
                        'width': video.get('webformatWidth', 1920),
                        'height': video.get('webformatHeight', 1080),
                        'tags': keywords,
                        'views': video.get('views', 0)
                    })

            self.logger.info(f"Pixabay搜索到{len(videos)}个视频")
            return videos

        except Exception as e:
            self.logger.error(f"Pixabay视频搜索失败: {e}")
            return []

    def _get_best_video_url(self, videos: dict) -> str:
        """选择最佳视频质量"""
        # 优先级：medium > small > tiny
        for quality in ['medium', 'small', 'tiny']:
            if quality in videos:
                return videos[quality]['url']
        return ""

    def download(self, url: str, output_path: str) -> bool:
        """下载Pixabay视频"""
        try:
            import requests

            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)

            response = requests.get(url, stream=True, timeout=30)
            response.raise_for_status()

            with open(output_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            self.logger.info(f"Pixabay视频下载成功: {output_path}")
            return True

        except Exception as e:
            self.logger.error(f"Pixabay视频下载失败: {e}")
            return False


class LocalVideoDownloader(VideoDownloader):
    """本地视频文件下载器"""

    def search(self, keywords: List[str], count: int = 10) -> List[Dict[str, Any]]:
        """搜索本地视频文件"""
        # 检查常见的视频文件位置
        common_paths = [
            Path("./videos"),
            Path("./video"),
            Path.home() / "Videos",
            Path.home() / "Downloads"
        ]

        results = []
        search_terms = [term.lower() for term in keywords if term]

        for base_path in common_paths:
            if not base_path.exists():
                continue

            for ext in ['.mp4', '.avi', '.mov', '.mkv', '.wmv']:
                for video_file in base_path.glob(f"**/*{ext}"):
                    filename = video_file.name.lower()

                    # 检查文件名是否包含搜索词
                    if not search_terms or any(term in filename for term in search_terms):
                        try:
                            file_size = video_file.stat().st_size
                            results.append({
                                'id': str(video_file),
                                'title': video_file.stem,
                                'url': f"file://{video_file}",
                                'thumbnail': '',
                                'duration': 30,  # 默认时长，实际会通过get_video_info获取
                                'width': 1920,
                                'height': 1080,
                                'tags': keywords,
                                'file_size': file_size
                            })
                        except Exception:
                            continue

        self.logger.info(f"找到{len(results)}个本地视频文件")
        return results[:count]

    def download(self, url: str, output_path: str) -> bool:
        """复制本地视频文件"""
        try:
            if not url.startswith("file://"):
                return False

            source_path = Path(url.replace("file://", ""))
            if not source_path.exists():
                self.logger.error(f"本地视频文件不存在: {source_path}")
                return False

            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)

            # 复制文件
            import shutil
            shutil.copy2(source_path, output_path)

            self.logger.info(f"本地视频文件复制成功: {output_path}")
            return True

        except Exception as e:
            self.logger.error(f"本地视频文件处理失败: {e}")
            return False


class VideoManager:
    """视频管理器"""
    
    def __init__(self):
        self.logger = get_logger("VideoManager")
        self.downloaders = {
            'pexels': PexelsVideoDownloader(),
            'pixabay': PixabayVideoDownloader(),
            'local': LocalVideoDownloader()
        }
        self.processor = VideoProcessor()
        self.temp_dir = config.get_temp_dir() / "video"
        self.temp_dir.mkdir(parents=True, exist_ok=True)
    
    def search_videos(self, keywords: List[str], count: int = 10, 
                     source: str = "mock") -> List[Dict[str, Any]]:
        """
        搜索视频
        
        Args:
            keywords: 搜索关键词
            count: 返回数量
            source: 视频源
            
        Returns:
            List[Dict]: 搜索结果
        """
        if source not in self.downloaders:
            self.logger.error(f"不支持的视频源: {source}")
            return []
        
        self.logger.info(f"搜索视频: {keywords} (来源: {source})")
        return self.downloaders[source].search(keywords, count)
    
    def download_videos(self, video_urls: List[str], 
                       source: str = "mock") -> List[str]:
        """
        批量下载视频
        
        Args:
            video_urls: 视频URL列表
            source: 视频源
            
        Returns:
            List[str]: 下载成功的文件路径列表
        """
        if source not in self.downloaders:
            self.logger.error(f"不支持的视频源: {source}")
            return []
        
        downloaded_files = []
        downloader = self.downloaders[source]
        
        # 使用线程池并行下载
        with ThreadPoolExecutor(max_workers=config.app_config['max_concurrent_downloads']) as executor:
            future_to_url = {}
            
            for i, url in enumerate(video_urls):
                filename = f"video_{i}_{hash(url) % 10000}.mp4"
                output_path = self.temp_dir / filename
                
                future = executor.submit(downloader.download, url, str(output_path))
                future_to_url[future] = str(output_path)
            
            for future in as_completed(future_to_url):
                output_path = future_to_url[future]
                try:
                    success = future.result()
                    if success:
                        downloaded_files.append(output_path)
                except Exception as e:
                    self.logger.error(f"下载视频失败: {e}")
        
        self.logger.info(f"成功下载{len(downloaded_files)}个视频文件")
        return downloaded_files
    
    def create_music_video(self, video_files: List[str], audio_file: str,
                          subtitle_file: str, output_path: str,
                          target_duration: float) -> bool:
        """
        创建音乐视频

        Args:
            video_files: 视频文件列表
            audio_file: 音频文件路径
            subtitle_file: 字幕文件路径
            output_path: 输出文件路径
            target_duration: 目标时长

        Returns:
            bool: 创建是否成功
        """
        try:
            # 验证输入
            if not video_files:
                self.logger.error("视频文件列表为空")
                return False

            if not os.path.exists(audio_file):
                self.logger.error(f"音频文件不存在: {audio_file}")
                return False

            # 验证视频文件存在性
            valid_video_files = []
            for video_file in video_files:
                if os.path.exists(video_file):
                    valid_video_files.append(video_file)
                else:
                    self.logger.warning(f"视频文件不存在，跳过: {video_file}")

            if not valid_video_files:
                self.logger.error("没有有效的视频文件")
                return False

            self.logger.info(f"开始创建音乐视频，使用{len(valid_video_files)}个视频文件")

            # 1. 处理视频片段，确保总时长匹配音频
            processed_clips = []
            current_duration = 0.0

            self.logger.info(f"目标视频时长: {target_duration:.1f}秒")

            # 循环处理视频文件，直到达到目标时长
            video_index = 0
            clip_index = 0

            while current_duration < target_duration and video_index < len(valid_video_files) * 3:  # 最多循环3次
                video_file = valid_video_files[video_index % len(valid_video_files)]

                self.logger.info(f"处理视频片段 {clip_index+1}: {Path(video_file).name}")

                # 获取视频信息
                clip_info = self.processor.get_video_info(video_file)
                if not clip_info:
                    self.logger.warning(f"无法获取视频信息，跳过: {video_file}")
                    video_index += 1
                    continue

                # 计算需要的片段长度
                max_clip_duration = config.app_config.get('max_clip_duration', 30)
                min_clip_duration = config.app_config.get('min_clip_duration', 3)

                remaining_duration = target_duration - current_duration

                # 如果剩余时间很少，使用剩余时间
                if remaining_duration <= max_clip_duration:
                    clip_duration = max(remaining_duration, min_clip_duration)
                else:
                    clip_duration = min(clip_info.duration, max_clip_duration)

                # 确保不超过视频本身的长度
                clip_duration = min(clip_duration, clip_info.duration)

                # 随机选择开始时间
                max_start = max(0, clip_info.duration - clip_duration)
                start_time = random.uniform(0, max_start) if max_start > 0 else 0

                # 裁剪视频片段
                clip_filename = f"clip_{clip_index:03d}.mp4"
                clip_path = self.temp_dir / clip_filename

                if self.processor.trim_video(video_file, str(clip_path),
                                           start_time, clip_duration):
                    processed_clips.append(str(clip_path))
                    current_duration += clip_duration
                    self.logger.info(f"视频片段处理成功: {clip_duration:.1f}秒 (总计: {current_duration:.1f}秒)")

                    # 抽帧功能（如果启用）
                    if config.app_config.get('frame_extract_enabled', False):
                        frame_dir = self.temp_dir / "frames" / f"clip_{clip_index:03d}"
                        extracted_frames = self.processor.extract_frames(
                            str(clip_path),
                            str(frame_dir),
                            config.app_config.get('frame_extract_interval', 5.0),
                            config.app_config.get('frame_extract_count', 3),
                            config.app_config.get('frame_extract_quality', 'high')
                        )

                        if extracted_frames:
                            self.logger.info(f"片段抽帧成功: {len(extracted_frames)}帧")

                    clip_index += 1

                    # 如果已经达到目标时长，退出
                    if current_duration >= target_duration:
                        break
                else:
                    self.logger.warning(f"视频片段处理失败: {video_file}")

                video_index += 1

            if not processed_clips:
                self.logger.error("没有成功处理的视频片段")
                return False

            # 检查时长差异
            duration_diff = abs(current_duration - target_duration)
            if duration_diff > 2.0:  # 如果差异超过2秒
                self.logger.warning(f"视频时长({current_duration:.1f}s)与音频时长({target_duration:.1f}s)差异较大: {duration_diff:.1f}s")
            else:
                self.logger.info(f"视频时长匹配良好: 视频{current_duration:.1f}s, 音频{target_duration:.1f}s")

            self.logger.info(f"成功处理{len(processed_clips)}个视频片段，总时长: {current_duration:.1f}秒")

            # 2. 拼接视频片段
            concatenated_video = self.temp_dir / "concatenated.mp4"
            self.logger.info("开始拼接视频片段...")

            if not self.processor.concatenate_videos(processed_clips, str(concatenated_video)):
                self.logger.error("视频拼接失败")
                return False

            self.logger.info("视频拼接完成")

            # 3. 添加音频
            video_with_audio = self.temp_dir / "with_audio.mp4"
            self.logger.info("开始添加音频...")

            if not self.processor.add_audio_to_video(str(concatenated_video),
                                                   audio_file,
                                                   str(video_with_audio)):
                self.logger.error("添加音频失败")
                return False

            self.logger.info("音频添加完成")

            # 4. 添加字幕（可选）
            video_with_subtitle = self.temp_dir / "with_subtitle.mp4"
            if subtitle_file and os.path.exists(subtitle_file):
                self.logger.info("开始添加字幕...")
                if not self.processor.add_subtitle_to_video(str(video_with_audio),
                                                          subtitle_file,
                                                          str(video_with_subtitle)):
                    self.logger.warning("字幕添加失败，使用无字幕版本")
                    # 字幕失败不影响整体流程，使用无字幕版本
                    import shutil
                    shutil.copy2(str(video_with_audio), str(video_with_subtitle))
                else:
                    self.logger.info("字幕添加完成")
            else:
                # 如果没有字幕文件，直接复制
                import shutil
                shutil.copy2(str(video_with_audio), str(video_with_subtitle))
                self.logger.info("无字幕文件，跳过字幕处理")

            # 5. 添加水印（可选）
            self.logger.info("开始添加水印...")
            if not self.processor.add_watermark_to_video(str(video_with_subtitle), output_path):
                self.logger.warning("水印添加失败，使用无水印版本")
                # 水印失败不影响整体流程，直接复制无水印版本
                import shutil
                shutil.copy2(str(video_with_subtitle), output_path)
            else:
                self.logger.info("水印添加完成")

            # 验证输出文件
            if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                self.logger.info(f"音乐视频创建成功: {output_path}")
                self.logger.info(f"输出文件大小: {os.path.getsize(output_path)} 字节")
                return True
            else:
                self.logger.error("输出文件创建失败或为空")
                return False

        except Exception as e:
            self.logger.error(f"创建音乐视频失败: {e}")
            import traceback
            self.logger.debug(f"详细错误信息: {traceback.format_exc()}")
            return False
    
    def cleanup_temp_files(self) -> None:
        """清理临时文件"""
        try:
            for file_path in self.temp_dir.glob("*"):
                if file_path.is_file():
                    file_path.unlink()
            self.logger.info("视频临时文件清理完成")
        except Exception as e:
            self.logger.error(f"清理视频临时文件失败: {e}")


# 全局视频管理器实例
video_manager = VideoManager()
