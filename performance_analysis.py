#!/usr/bin/env python3
"""
性能分析和优化脚本
检查各模块的性能瓶颈并提供优化建议
"""

import sys
import time
import threading
from pathlib import Path
import tempfile
import os

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def analyze_video_processing():
    """分析视频处理性能"""
    print("📹 分析视频处理性能...")
    
    try:
        from src.video.video_manager import video_manager
        
        # 查找测试文件
        video_dir = Path("./videos")
        video_files = list(video_dir.glob("*.mp4"))
        
        if not video_files:
            print("⚠ 未找到测试视频文件")
            return {}
        
        test_video = video_files[0]
        results = {}
        
        # 测试视频信息获取速度
        start_time = time.time()
        video_info = video_manager.processor.get_video_info(str(test_video))
        info_time = time.time() - start_time
        results['video_info'] = info_time
        
        if video_info:
            print(f"  视频信息获取: {info_time:.2f}秒")
        
        # 测试视频裁剪速度
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as f:
            output_path = f.name
        
        try:
            start_time = time.time()
            success = video_manager.processor.trim_video(
                str(test_video), output_path, 0, 5  # 裁剪5秒
            )
            trim_time = time.time() - start_time
            results['video_trim'] = trim_time
            
            if success:
                print(f"  视频裁剪(5秒): {trim_time:.2f}秒")
            
        finally:
            if os.path.exists(output_path):
                os.unlink(output_path)
        
        return results
        
    except Exception as e:
        print(f"✗ 视频处理分析失败: {e}")
        return {}

def analyze_audio_processing():
    """分析音频处理性能"""
    print("\n🎵 分析音频处理性能...")
    
    try:
        from src.audio.audio_manager import audio_manager
        
        # 查找测试文件
        audio_dir = Path("./audio")
        audio_files = list(audio_dir.glob("*.mp3"))
        
        if not audio_files:
            print("⚠ 未找到测试音频文件")
            return {}
        
        test_audio = audio_files[0]
        results = {}
        
        # 测试音频信息获取速度
        start_time = time.time()
        audio_info = audio_manager.get_audio_info(str(test_audio))
        info_time = time.time() - start_time
        results['audio_info'] = info_time
        
        if audio_info:
            print(f"  音频信息获取: {info_time:.2f}秒")
        
        return results
        
    except Exception as e:
        print(f"✗ 音频处理分析失败: {e}")
        return {}

def analyze_subtitle_processing():
    """分析字幕处理性能"""
    print("\n📝 分析字幕处理性能...")
    
    try:
        from src.subtitle.subtitle_manager import subtitle_manager
        
        test_lyrics = """这是第一行歌词
这是第二行歌词
这是第三行歌词
这是第四行歌词"""
        
        results = {}
        
        # 测试字幕生成速度
        with tempfile.NamedTemporaryFile(suffix='.srt', delete=False) as f:
            subtitle_path = f.name
        
        try:
            start_time = time.time()
            success = subtitle_manager.create_subtitle(test_lyrics, 20.0, subtitle_path, "srt")
            subtitle_time = time.time() - start_time
            results['subtitle_generation'] = subtitle_time
            
            if success:
                print(f"  字幕生成: {subtitle_time:.2f}秒")
            
        finally:
            if os.path.exists(subtitle_path):
                os.unlink(subtitle_path)
        
        return results
        
    except Exception as e:
        print(f"✗ 字幕处理分析失败: {e}")
        return {}

def analyze_network_performance():
    """分析网络请求性能"""
    print("\n🌐 分析网络请求性能...")
    
    try:
        import requests
        
        results = {}
        
        # 测试网络连接速度
        test_urls = [
            "https://httpbin.org/get",
            "https://www.baidu.com",
            "https://api.github.com"
        ]
        
        for url in test_urls:
            try:
                start_time = time.time()
                response = requests.get(url, timeout=5)
                request_time = time.time() - start_time
                
                if response.status_code == 200:
                    print(f"  {url}: {request_time:.2f}秒")
                    results[url] = request_time
                else:
                    print(f"  {url}: 失败 ({response.status_code})")
                    
            except Exception as e:
                print(f"  {url}: 超时或错误")
        
        return results
        
    except Exception as e:
        print(f"✗ 网络性能分析失败: {e}")
        return {}

def identify_bottlenecks(results):
    """识别性能瓶颈"""
    print("\n🔍 性能瓶颈分析:")
    
    bottlenecks = []
    
    # 视频处理瓶颈
    if 'video_trim' in results and results['video_trim'] > 10:
        bottlenecks.append(f"视频裁剪过慢: {results['video_trim']:.2f}秒")
    
    # 音频处理瓶颈
    if 'audio_info' in results and results['audio_info'] > 2:
        bottlenecks.append(f"音频信息获取过慢: {results['audio_info']:.2f}秒")
    
    # 网络请求瓶颈
    network_times = [v for k, v in results.items() if 'http' in k]
    if network_times and max(network_times) > 3:
        bottlenecks.append(f"网络请求过慢: 最大{max(network_times):.2f}秒")
    
    if bottlenecks:
        for bottleneck in bottlenecks:
            print(f"  ⚠ {bottleneck}")
    else:
        print("  ✓ 未发现明显性能瓶颈")
    
    return bottlenecks

def suggest_optimizations():
    """提供优化建议"""
    print("\n💡 性能优化建议:")
    
    suggestions = [
        "1. 视频处理优化:",
        "   • 使用GPU加速 (如果支持)",
        "   • 降低视频质量进行预处理",
        "   • 并行处理多个视频片段",
        "   • 缓存视频信息避免重复解析",
        "",
        "2. 音频处理优化:",
        "   • 缓存音频元数据",
        "   • 使用更快的音频解码器",
        "   • 预加载常用音频格式",
        "",
        "3. 网络请求优化:",
        "   • 实现请求缓存机制",
        "   • 使用连接池",
        "   • 并行下载多个文件",
        "   • 添加重试机制",
        "",
        "4. 内存优化:",
        "   • 流式处理大文件",
        "   • 及时释放临时资源",
        "   • 使用内存映射文件",
        "",
        "5. 并发优化:",
        "   • 使用线程池处理IO密集任务",
        "   • 异步处理网络请求",
        "   • 并行处理独立的视频片段"
    ]
    
    for suggestion in suggestions:
        print(f"  {suggestion}")

def check_system_resources():
    """检查系统资源"""
    print("\n💻 系统资源检查:")
    
    try:
        import psutil
        
        # CPU信息
        cpu_count = psutil.cpu_count()
        cpu_percent = psutil.cpu_percent(interval=1)
        print(f"  CPU: {cpu_count}核心, 使用率: {cpu_percent}%")
        
        # 内存信息
        memory = psutil.virtual_memory()
        print(f"  内存: {memory.total // (1024**3)}GB 总计, {memory.percent}% 使用")
        
        # 磁盘信息
        disk = psutil.disk_usage('.')
        print(f"  磁盘: {disk.total // (1024**3)}GB 总计, {disk.percent}% 使用")
        
        # 性能建议
        if cpu_percent > 80:
            print("  ⚠ CPU使用率较高，建议关闭其他程序")
        if memory.percent > 80:
            print("  ⚠ 内存使用率较高，建议释放内存")
        if disk.percent > 90:
            print("  ⚠ 磁盘空间不足，建议清理临时文件")
        
    except ImportError:
        print("  ⚠ 未安装psutil，无法获取系统资源信息")
        print("  建议安装: pip install psutil")
    except Exception as e:
        print(f"  ✗ 系统资源检查失败: {e}")

def main():
    """主函数"""
    print("🔧 AI音乐视频生成器 - 性能分析")
    print("=" * 50)
    
    # 检查系统资源
    check_system_resources()
    
    # 分析各模块性能
    all_results = {}
    
    video_results = analyze_video_processing()
    all_results.update(video_results)
    
    audio_results = analyze_audio_processing()
    all_results.update(audio_results)
    
    subtitle_results = analyze_subtitle_processing()
    all_results.update(subtitle_results)
    
    network_results = analyze_network_performance()
    all_results.update(network_results)
    
    # 识别瓶颈
    bottlenecks = identify_bottlenecks(all_results)
    
    # 提供优化建议
    suggest_optimizations()
    
    print("\n" + "=" * 50)
    print("📊 性能分析完成")
    
    if bottlenecks:
        print(f"发现 {len(bottlenecks)} 个性能瓶颈，建议优化")
    else:
        print("系统性能良好")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
