"""
AI音乐视频生成器主程序
基于Python 3.12和PySide6开发的音乐视频一键生成软件
支持自动获取音频、歌词和视频素材，一键生成音乐视频
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt, QTranslator, QLocale
from PySide6.QtGui import QIcon

from src.logger.logger import main_logger
from src.config.settings import config
from src.theme.theme_manager import theme_manager
from src.ui.main_window import MainWindow


class Application:
    """应用程序类"""
    
    def __init__(self):
        self.app = None
        self.main_window = None
        self.logger = main_logger
    
    def initialize(self):
        """初始化应用程序"""
        try:
            # 创建QApplication实例
            self.app = QApplication(sys.argv)
            self.app.setApplicationName("AI音乐视频生成器")
            self.app.setApplicationVersion("1.0")
            self.app.setOrganizationName("AI Music Video Generator")
            
            # 设置应用程序图标（如果有的话）
            # icon_path = project_root / "resources" / "icon.ico"
            # if icon_path.exists():
            #     self.app.setWindowIcon(QIcon(str(icon_path)))
            
            
            # 初始化配置
            self.logger.info("初始化配置...")
            config.load_config()
            
            # 应用主题
            self.logger.info("应用主题...")
            theme_manager.set_theme(config.app_config.get('theme', 'light'))
            
            # 创建主窗口
            self.logger.info("创建主窗口...")
            self.main_window = MainWindow()
            
            self.logger.info("应用程序初始化完成")
            return True
            
        except Exception as e:
            self.logger.exception("应用程序初始化失败")
            if self.app:
                QMessageBox.critical(
                    None,
                    "初始化错误",
                    f"应用程序初始化失败：\n{str(e)}"
                )
            return False
    
    def run(self):
        """运行应用程序"""
        if not self.initialize():
            return 1
        
        try:
            # 显示主窗口
            self.main_window.show()
            
            # 启动事件循环
            self.logger.info("启动应用程序...")
            return self.app.exec()
            
        except Exception as e:
            self.logger.exception("应用程序运行时发生错误")
            QMessageBox.critical(
                self.main_window,
                "运行错误",
                f"应用程序运行时发生错误：\n{str(e)}"
            )
            return 1
        
        finally:
            self.cleanup()
    
    def cleanup(self):
        """清理资源"""
        try:
            self.logger.info("清理应用程序资源...")
            
            # 保存配置
            config.save_config()
            
            # 清理临时文件（可选）
            if config.app_config.get('auto_cleanup', True):
                from src.core.controller import controller
                controller.cleanup_temp_files()
            
            self.logger.info("应用程序退出")
            
        except Exception as e:
            self.logger.exception("清理资源时发生错误")


def check_dependencies():
    """检查依赖项"""
    missing_deps = []
    
    try:
        import PySide6
    except ImportError:
        missing_deps.append("PySide6")
    
    try:
        import requests
    except ImportError:
        missing_deps.append("requests")
    
    try:
        import ffmpeg
    except ImportError:
        missing_deps.append("ffmpeg-python")
    
    try:
        import pysrt
    except ImportError:
        missing_deps.append("pysrt")
    
    try:
        import yaml
    except ImportError:
        missing_deps.append("PyYAML")
    
    try:
        import colorlog
    except ImportError:
        missing_deps.append("colorlog")
    
    if missing_deps:
        print("错误：缺少以下依赖项：")
        for dep in missing_deps:
            print(f"  - {dep}")
        print("\n请运行以下命令安装依赖项：")
        print("pip install -r requirements.txt")
        return False
    
    return True


def check_ffmpeg():
    """检查FFmpeg是否可用"""
    try:
        import subprocess
        result = subprocess.run(
            ['ffmpeg', '-version'],
            capture_output=True,
            text=True,
            timeout=5
        )
        return result.returncode == 0
    except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
        return False


def main():
    """主函数"""
    print("AI音乐视频生成器 v1.0")
    print("=" * 50)
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("错误：需要Python 3.8或更高版本")
        return 1
    
    # 检查依赖项
    print("检查依赖项...")
    if not check_dependencies():
        return 1
    
    # 检查FFmpeg
    print("检查FFmpeg...")
    if not check_ffmpeg():
        print("警告：未检测到FFmpeg，视频处理功能可能无法正常工作")
        print("请访问 https://ffmpeg.org/download.html 下载并安装FFmpeg")
        
        # 询问是否继续
        try:
            response = input("是否继续启动程序？(y/N): ").strip().lower()
            if response not in ['y', 'yes']:
                return 1
        except KeyboardInterrupt:
            print("\n用户取消")
            return 1
    
    # 创建必要的目录
    try:
        config_dir = Path.home() / ".ai_music"
        config_dir.mkdir(exist_ok=True)
        
        temp_dir = config_dir / "temp"
        temp_dir.mkdir(exist_ok=True)
        
        output_dir = config_dir / "output"
        output_dir.mkdir(exist_ok=True)
        
        cache_dir = config_dir / "cache"
        cache_dir.mkdir(exist_ok=True)
        
        logs_dir = config_dir / "logs"
        logs_dir.mkdir(exist_ok=True)
        
    except Exception as e:
        print(f"错误：无法创建必要的目录：{e}")
        return 1
    
    # 启动应用程序
    print("启动应用程序...")
    app = Application()
    return app.run()


if __name__ == "__main__":
    sys.exit(main())
