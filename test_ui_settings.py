#!/usr/bin/env python3
"""
测试用户界面和设置功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_settings_dialog():
    """测试设置对话框"""
    try:
        from PySide6.QtWidgets import QApplication
        from src.ui.settings_dialog import SettingsDialog
        
        # 创建应用程序实例
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # 创建设置对话框
        dialog = SettingsDialog()
        
        print("✓ 设置对话框创建成功")
        print(f"  - 选项卡数量: {dialog.findChild(QApplication.instance().allWidgets()[0].__class__.__name__)}")
        
        # 检查各个控件是否存在
        controls = [
            'pexels_api_edit',
            'pixabay_api_edit', 
            'resolution_combo',
            'fps_combo',
            'video_bitrate_combo',
            'audio_bitrate_combo'
        ]
        
        for control_name in controls:
            if hasattr(dialog, control_name):
                print(f"  ✓ {control_name} 存在")
            else:
                print(f"  ✗ {control_name} 不存在")
        
        return True
        
    except Exception as e:
        print(f"✗ 设置对话框测试失败: {e}")
        return False

def test_main_window():
    """测试主窗口"""
    try:
        from PySide6.QtWidgets import QApplication
        from src.ui.main_window import MainWindow
        
        # 创建应用程序实例
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # 创建主窗口
        window = MainWindow()
        
        print("✓ 主窗口创建成功")
        
        # 检查下拉框选项
        audio_items = [window.audio_source_combo.itemText(i) for i in range(window.audio_source_combo.count())]
        video_items = [window.video_source_combo.itemText(i) for i in range(window.video_source_combo.count())]
        
        print(f"  - 音频源选项: {audio_items}")
        print(f"  - 视频源选项: {video_items}")
        
        # 验证选项数量
        if len(audio_items) >= 2:
            print("  ✓ 音频源选项数量正常")
        else:
            print("  ✗ 音频源选项数量不足")
        
        if len(video_items) >= 2:
            print("  ✓ 视频源选项数量正常")
        else:
            print("  ✗ 视频源选项数量不足")
        
        return True
        
    except Exception as e:
        print(f"✗ 主窗口测试失败: {e}")
        return False

def test_config_loading():
    """测试配置加载"""
    try:
        from src.config.settings import config
        
        print("✓ 配置加载成功")
        print(f"  - 视频分辨率: {config.video.width}x{config.video.height}")
        print(f"  - 视频帧率: {config.video.fps}")
        print(f"  - 视频码率: {config.video.bitrate}k")
        print(f"  - 视频编码器: {config.video.codec}")
        print(f"  - 音频码率: {config.audio.bitrate}k")
        print(f"  - 音频编码器: {config.audio.codec}")
        print(f"  - 最大片段长度: {config.app_config.get('max_clip_duration')}秒")
        print(f"  - 最小片段长度: {config.app_config.get('min_clip_duration')}秒")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置测试失败: {e}")
        return False

def test_video_manager():
    """测试视频管理器"""
    try:
        from src.video.video_manager import video_manager
        
        print("✓ 视频管理器加载成功")
        print(f"  - 可用下载器: {list(video_manager.downloaders.keys())}")
        
        # 测试本地视频搜索
        results = video_manager.search_videos(["test"], 5, "local")
        print(f"  - 本地视频搜索结果: {len(results)}个")
        
        return True
        
    except Exception as e:
        print(f"✗ 视频管理器测试失败: {e}")
        return False

def main():
    """主函数"""
    print("AI音乐视频生成器 - 界面和设置测试")
    print("=" * 50)
    
    tests = [
        test_config_loading,
        test_video_manager,
        test_main_window,
        test_settings_dialog
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()  # 空行分隔
        except Exception as e:
            print(f"✗ 测试异常: {e}")
            print()
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有界面和设置测试通过！")
        return 0
    else:
        print("⚠ 部分测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
