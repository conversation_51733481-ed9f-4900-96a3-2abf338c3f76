#!/usr/bin/env python3
"""
创建测试视频文件
用于测试视频处理功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def create_test_videos():
    """创建测试视频文件"""
    try:
        from src.config.settings import config
        import subprocess
        
        # 获取FFmpeg路径
        ffmpeg_path = config.get_ffmpeg_path()
        
        # 创建视频目录
        video_dir = Path("./videos")
        video_dir.mkdir(exist_ok=True)
        
        # 创建多个测试视频文件
        video_configs = [
            {
                'name': 'test_video_1.mp4',
                'color': 'red',
                'duration': 15,
                'text': 'Video 1'
            },
            {
                'name': 'test_video_2.mp4', 
                'color': 'green',
                'duration': 20,
                'text': 'Video 2'
            },
            {
                'name': 'test_video_3.mp4',
                'color': 'blue', 
                'duration': 18,
                'text': 'Video 3'
            }
        ]
        
        success_count = 0
        
        for config_item in video_configs:
            video_path = video_dir / config_item['name']
            
            print(f"创建测试视频: {video_path}")
            
            # 使用FFmpeg生成彩色测试视频
            cmd = [
                str(ffmpeg_path),
                '-f', 'lavfi',
                '-i', f"color=c={config_item['color']}:s=1920x1080:d={config_item['duration']}",
                '-f', 'lavfi', 
                '-i', f"sine=frequency=440:duration={config_item['duration']}",
                '-vf', f"drawtext=text='{config_item['text']}':fontsize=60:fontcolor=white:x=(w-text_w)/2:y=(h-text_h)/2",
                '-c:v', 'libx264',
                '-c:a', 'aac',
                '-preset', 'fast',
                '-crf', '23',
                '-r', '30',
                '-y',  # 覆盖已存在的文件
                str(video_path)
            ]
            
            try:
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
                
                if result.returncode == 0:
                    print(f"✓ 测试视频创建成功: {video_path}")
                    print(f"  文件大小: {video_path.stat().st_size} 字节")
                    print(f"  时长: {config_item['duration']} 秒")
                    success_count += 1
                else:
                    print(f"✗ 创建测试视频失败: {config_item['name']}")
                    print(f"  错误: {result.stderr}")
                    
            except subprocess.TimeoutExpired:
                print(f"✗ 创建测试视频超时: {config_item['name']}")
            except Exception as e:
                print(f"✗ 创建测试视频异常: {config_item['name']}, {e}")
        
        return success_count == len(video_configs)
        
    except Exception as e:
        print(f"✗ 创建测试视频异常: {e}")
        return False

def test_video_processing():
    """测试视频处理功能"""
    try:
        from src.video.video_manager import video_manager
        
        video_dir = Path("./videos")
        video_files = list(video_dir.glob("*.mp4"))
        
        if not video_files:
            print("⚠ 未找到测试视频文件")
            return False
        
        print(f"\n测试视频处理，找到{len(video_files)}个视频文件:")
        
        # 测试获取视频信息
        for video_file in video_files:
            print(f"\n测试视频: {video_file.name}")
            
            clip_info = video_manager.processor.get_video_info(str(video_file))
            if clip_info:
                print(f"✓ 视频信息获取成功:")
                print(f"  - 时长: {clip_info.duration} 秒")
                print(f"  - 尺寸: {clip_info.width}x{clip_info.height}")
                print(f"  - 帧率: {clip_info.fps}")
            else:
                print(f"✗ 视频信息获取失败")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ 视频处理测试异常: {e}")
        return False

def test_video_concatenation():
    """测试视频拼接功能"""
    try:
        from src.video.video_manager import video_manager
        import tempfile
        import os
        
        video_dir = Path("./videos")
        video_files = [str(f) for f in video_dir.glob("*.mp4")]
        
        if len(video_files) < 2:
            print("⚠ 需要至少2个视频文件进行拼接测试")
            return False
        
        print(f"\n测试视频拼接，使用{len(video_files)}个视频文件")
        
        # 创建临时输出文件
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as f:
            temp_output = f.name
        
        try:
            success = video_manager.processor.concatenate_videos(video_files, temp_output)
            
            if success and os.path.exists(temp_output) and os.path.getsize(temp_output) > 0:
                print(f"✓ 视频拼接测试成功")
                print(f"  - 输出文件: {temp_output}")
                print(f"  - 文件大小: {os.path.getsize(temp_output)} 字节")
                return True
            else:
                print(f"✗ 视频拼接测试失败")
                return False
                
        finally:
            # 清理临时文件
            if os.path.exists(temp_output):
                os.unlink(temp_output)
        
    except Exception as e:
        print(f"✗ 视频拼接测试异常: {e}")
        return False

def main():
    """主函数"""
    print("AI音乐视频生成器 - 测试视频创建")
    print("=" * 40)
    
    # 创建测试视频
    if create_test_videos():
        print("\n🎉 测试视频创建成功！")
    else:
        print("\n❌ 测试视频创建失败")
        return 1
    
    # 测试视频处理
    if test_video_processing():
        print("\n✓ 视频处理测试通过")
    else:
        print("\n✗ 视频处理测试失败")
        return 1
    
    # 测试视频拼接
    if test_video_concatenation():
        print("\n✓ 视频拼接测试通过")
    else:
        print("\n✗ 视频拼接测试失败")
        return 1
    
    print("\n🎉 所有视频测试通过！")
    print("\n使用方法：")
    print("1. 在程序中选择视频源为本地文件")
    print("2. 或者将测试视频文件放在合适的目录")
    print("3. 开始生成音乐视频")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
