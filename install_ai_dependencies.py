#!/usr/bin/env python3
"""
安装AI功能依赖脚本
"""

import subprocess
import sys
from pathlib import Path

def install_package(package_name, description=""):
    """安装Python包"""
    try:
        print(f"📦 安装 {package_name}...")
        if description:
            print(f"   {description}")
        
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package_name
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"   ✅ {package_name} 安装成功")
            return True
        else:
            print(f"   ❌ {package_name} 安装失败: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"   ⏰ {package_name} 安装超时")
        return False
    except Exception as e:
        print(f"   ❌ {package_name} 安装异常: {e}")
        return False

def main():
    """主函数"""
    print("🤖 AI音乐视频生成器 - AI功能依赖安装")
    print("=" * 50)
    
    # 需要安装的包列表
    packages = [
        ("google-genai", "Google Gemini AI SDK - 用于文本和图片生成"),
        ("Pillow", "Python图像处理库 - 用于图片处理"),
        ("requests", "HTTP请求库 - 用于API调用"),
        ("psutil", "系统资源监控库 - 用于性能分析"),
    ]
    
    installed = 0
    total = len(packages)
    
    for package, description in packages:
        if install_package(package, description):
            installed += 1
        print()
    
    print("=" * 50)
    print(f"安装结果: {installed}/{total} 成功")
    
    if installed == total:
        print("🎉 所有AI功能依赖安装完成！")
        print("\n📋 已安装的功能:")
        print("• Gemini AI - 文本分析和图片生成")
        print("• 智谱AI - 文本分析和图片生成")
        print("• DeepSeek - 文本分析")
        print("• SiliconFlow - 图片生成")
        print("• 性能监控 - 系统资源分析")
        
        print("\n🔧 配置说明:")
        print("1. 在设置中配置相应的API密钥")
        print("2. 选择合适的AI模型进行歌词分析")
        print("3. 选择图片生成模型和风格")
        print("4. 勾选'使用AI生成图片'选项")
        
        print("\n🚀 使用方法:")
        print("1. 输入歌曲名称和歌手")
        print("2. 勾选'使用AI生成图片'")
        print("3. 选择分析模型和图片模型")
        print("4. 选择图片风格")
        print("5. 点击开始生成")
        
        return 0
    else:
        print("⚠️ 部分依赖安装失败")
        print("请检查网络连接或手动安装失败的包")
        return 1

if __name__ == "__main__":
    sys.exit(main())
