"""
AI模型设置对话框
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget,
    QLabel, QLineEdit, QSpinBox, QDoubleSpinBox, QTextEdit,
    QPushButton, QGroupBox, QGridLayout, QComboBox, QMessageBox
)
from PySide6.QtCore import Qt

from src.config.settings import config


class AISettingsDialog(QDialog):
    """AI模型设置对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("AI模型设置")
        self.setModal(True)
        self.resize(600, 500)
        
        self.init_ui()
        self.load_settings()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 文本模型标签页
        self.text_tab = QWidget()
        self.tab_widget.addTab(self.text_tab, "文本模型")
        self.init_text_tab()
        
        # 图像模型标签页
        self.image_tab = QWidget()
        self.tab_widget.addTab(self.image_tab, "图像模型")
        self.init_image_tab()
        
        # 按钮
        button_layout = QHBoxLayout()
        
        self.test_button = QPushButton("测试连接")
        self.test_button.clicked.connect(self.test_connection)
        button_layout.addWidget(self.test_button)
        
        button_layout.addStretch()
        
        self.save_button = QPushButton("保存")
        self.save_button.clicked.connect(self.save_settings)
        button_layout.addWidget(self.save_button)
        
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
    
    def init_text_tab(self):
        """初始化文本模型标签页"""
        layout = QVBoxLayout(self.text_tab)
        
        # 模型选择
        model_layout = QHBoxLayout()
        model_layout.addWidget(QLabel("选择模型:"))
        self.text_model_combo = QComboBox()
        self.text_model_combo.addItems(["gemini", "zhipu", "deepseek"])
        self.text_model_combo.currentTextChanged.connect(self.on_text_model_changed)
        model_layout.addWidget(self.text_model_combo)
        model_layout.addStretch()
        layout.addLayout(model_layout)
        
        # 创建各模型的设置组
        self.text_settings = {}
        for model_name in ["gemini", "zhipu", "deepseek"]:
            group = self.create_text_model_group(model_name)
            self.text_settings[model_name] = group
            layout.addWidget(group)
        
        layout.addStretch()
    
    def create_text_model_group(self, model_name):
        """创建文本模型设置组"""
        group = QGroupBox(f"{model_name.upper()} 设置")
        layout = QGridLayout(group)
        
        # API密钥
        layout.addWidget(QLabel("API密钥:"), 0, 0)
        api_key_edit = QLineEdit()
        api_key_edit.setEchoMode(QLineEdit.Password)
        api_key_edit.setPlaceholderText("请输入API密钥")
        layout.addWidget(api_key_edit, 0, 1)
        
        # 模型名称
        layout.addWidget(QLabel("模型名称:"), 1, 0)
        model_name_edit = QLineEdit()
        layout.addWidget(model_name_edit, 1, 1)
        
        # 基础URL
        layout.addWidget(QLabel("基础URL:"), 2, 0)
        base_url_edit = QLineEdit()
        layout.addWidget(base_url_edit, 2, 1)
        
        # 最大令牌数
        layout.addWidget(QLabel("最大令牌数:"), 3, 0)
        max_tokens_spin = QSpinBox()
        max_tokens_spin.setRange(100, 8192)
        max_tokens_spin.setValue(2048)
        layout.addWidget(max_tokens_spin, 3, 1)
        
        # 温度
        layout.addWidget(QLabel("温度:"), 4, 0)
        temperature_spin = QDoubleSpinBox()
        temperature_spin.setRange(0.0, 2.0)
        temperature_spin.setSingleStep(0.1)
        temperature_spin.setValue(0.7)
        layout.addWidget(temperature_spin, 4, 1)
        
        # 系统提示词
        layout.addWidget(QLabel("系统提示词:"), 5, 0)
        system_prompt_edit = QTextEdit()
        system_prompt_edit.setMaximumHeight(100)
        layout.addWidget(system_prompt_edit, 5, 1)
        
        # 保存控件引用
        group.api_key_edit = api_key_edit
        group.model_name_edit = model_name_edit
        group.base_url_edit = base_url_edit
        group.max_tokens_spin = max_tokens_spin
        group.temperature_spin = temperature_spin
        group.system_prompt_edit = system_prompt_edit
        
        return group
    
    def init_image_tab(self):
        """初始化图像模型标签页"""
        layout = QVBoxLayout(self.image_tab)
        
        # 模型选择
        model_layout = QHBoxLayout()
        model_layout.addWidget(QLabel("选择模型:"))
        self.image_model_combo = QComboBox()
        self.image_model_combo.addItems(["siliconflow", "gemini", "zhipu", "doubao"])
        self.image_model_combo.currentTextChanged.connect(self.on_image_model_changed)
        model_layout.addWidget(self.image_model_combo)
        model_layout.addStretch()
        layout.addLayout(model_layout)
        
        # 创建各模型的设置组
        self.image_settings = {}
        for model_name in ["siliconflow", "gemini", "zhipu", "doubao"]:
            group = self.create_image_model_group(model_name)
            self.image_settings[model_name] = group
            layout.addWidget(group)
        
        layout.addStretch()
    
    def create_image_model_group(self, model_name):
        """创建图像模型设置组"""
        group = QGroupBox(f"{model_name.upper()} 设置")
        layout = QGridLayout(group)
        
        # API密钥
        layout.addWidget(QLabel("API密钥:"), 0, 0)
        api_key_edit = QLineEdit()
        api_key_edit.setEchoMode(QLineEdit.Password)
        api_key_edit.setPlaceholderText("请输入API密钥")
        layout.addWidget(api_key_edit, 0, 1)
        
        # 模型名称
        layout.addWidget(QLabel("模型名称:"), 1, 0)
        model_name_edit = QLineEdit()
        layout.addWidget(model_name_edit, 1, 1)
        
        # 基础URL
        layout.addWidget(QLabel("基础URL:"), 2, 0)
        base_url_edit = QLineEdit()
        layout.addWidget(base_url_edit, 2, 1)
        
        # 图片尺寸
        layout.addWidget(QLabel("图片尺寸:"), 3, 0)
        image_size_combo = QComboBox()
        image_size_combo.addItems(["512x512", "768x768", "1024x1024", "1280x720", "1920x1080"])
        layout.addWidget(image_size_combo, 3, 1)
        
        # 特定参数（根据模型不同）
        if model_name == "siliconflow":
            # 批次大小
            layout.addWidget(QLabel("批次大小:"), 4, 0)
            batch_size_spin = QSpinBox()
            batch_size_spin.setRange(1, 4)
            batch_size_spin.setValue(1)
            layout.addWidget(batch_size_spin, 4, 1)
            
            # 推理步数
            layout.addWidget(QLabel("推理步数:"), 5, 0)
            steps_spin = QSpinBox()
            steps_spin.setRange(1, 50)
            steps_spin.setValue(20)
            layout.addWidget(steps_spin, 5, 1)
            
            # 引导比例
            layout.addWidget(QLabel("引导比例:"), 6, 0)
            guidance_spin = QDoubleSpinBox()
            guidance_spin.setRange(1.0, 20.0)
            guidance_spin.setSingleStep(0.5)
            guidance_spin.setValue(7.5)
            layout.addWidget(guidance_spin, 6, 1)
            
            group.batch_size_spin = batch_size_spin
            group.steps_spin = steps_spin
            group.guidance_spin = guidance_spin
        
        # 保存控件引用
        group.api_key_edit = api_key_edit
        group.model_name_edit = model_name_edit
        group.base_url_edit = base_url_edit
        group.image_size_combo = image_size_combo
        
        return group
    
    def on_text_model_changed(self, model_name):
        """文本模型选择变化"""
        for name, group in self.text_settings.items():
            group.setVisible(name == model_name)
    
    def on_image_model_changed(self, model_name):
        """图像模型选择变化"""
        for name, group in self.image_settings.items():
            group.setVisible(name == model_name)
    
    def load_settings(self):
        """加载设置"""
        # 加载文本模型设置
        for model_name, group in self.text_settings.items():
            model_config = config.text_models.get(model_name, {})
            
            group.api_key_edit.setText(model_config.get("api_key", ""))
            group.model_name_edit.setText(model_config.get("model_name", ""))
            group.base_url_edit.setText(model_config.get("base_url", ""))
            group.max_tokens_spin.setValue(model_config.get("max_tokens", 2048))
            group.temperature_spin.setValue(model_config.get("temperature", 0.7))
            group.system_prompt_edit.setPlainText(model_config.get("system_prompt", ""))
        
        # 加载图像模型设置
        for model_name, group in self.image_settings.items():
            model_config = config.image_models.get(model_name, {})
            
            group.api_key_edit.setText(model_config.get("api_key", ""))
            group.model_name_edit.setText(model_config.get("model_name", ""))
            group.base_url_edit.setText(model_config.get("base_url", ""))
            group.image_size_combo.setCurrentText(model_config.get("image_size", "1024x1024"))
            
            # 特定参数
            if model_name == "siliconflow" and hasattr(group, 'batch_size_spin'):
                group.batch_size_spin.setValue(model_config.get("batch_size", 1))
                group.steps_spin.setValue(model_config.get("num_inference_steps", 20))
                group.guidance_spin.setValue(model_config.get("guidance_scale", 7.5))
        
        # 设置初始显示
        self.on_text_model_changed(self.text_model_combo.currentText())
        self.on_image_model_changed(self.image_model_combo.currentText())
    
    def save_settings(self):
        """保存设置"""
        try:
            # 保存文本模型设置
            for model_name, group in self.text_settings.items():
                config.text_models[model_name].update({
                    "api_key": group.api_key_edit.text().strip(),
                    "model_name": group.model_name_edit.text().strip(),
                    "base_url": group.base_url_edit.text().strip(),
                    "max_tokens": group.max_tokens_spin.value(),
                    "temperature": group.temperature_spin.value(),
                    "system_prompt": group.system_prompt_edit.toPlainText().strip()
                })
            
            # 保存图像模型设置
            for model_name, group in self.image_settings.items():
                config.image_models[model_name].update({
                    "api_key": group.api_key_edit.text().strip(),
                    "model_name": group.model_name_edit.text().strip(),
                    "base_url": group.base_url_edit.text().strip(),
                    "image_size": group.image_size_combo.currentText()
                })
                
                # 特定参数
                if model_name == "siliconflow" and hasattr(group, 'batch_size_spin'):
                    config.image_models[model_name].update({
                        "batch_size": group.batch_size_spin.value(),
                        "num_inference_steps": group.steps_spin.value(),
                        "guidance_scale": group.guidance_spin.value()
                    })
            
            # 保存配置到文件
            config.save_config()

            QMessageBox.information(self, "成功", "AI模型设置已保存")
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存设置失败: {e}")
    
    def test_connection(self):
        """测试连接"""
        current_tab = self.tab_widget.currentIndex()
        
        if current_tab == 0:  # 文本模型
            model_name = self.text_model_combo.currentText()
            self.test_text_model(model_name)
        else:  # 图像模型
            model_name = self.image_model_combo.currentText()
            self.test_image_model(model_name)
    
    def test_text_model(self, model_name):
        """测试文本模型连接"""
        try:
            group = self.text_settings[model_name]
            api_key = group.api_key_edit.text().strip()
            
            if not api_key:
                QMessageBox.warning(self, "警告", "请先输入API密钥")
                return
            
            # 这里可以添加实际的连接测试逻辑
            QMessageBox.information(self, "测试", f"{model_name} 文本模型连接测试功能待实现")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"测试连接失败: {e}")
    
    def test_image_model(self, model_name):
        """测试图像模型连接"""
        try:
            group = self.image_settings[model_name]
            api_key = group.api_key_edit.text().strip()
            
            if not api_key:
                QMessageBox.warning(self, "警告", "请先输入API密钥")
                return
            
            # 这里可以添加实际的连接测试逻辑
            QMessageBox.information(self, "测试", f"{model_name} 图像模型连接测试功能待实现")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"测试连接失败: {e}")
